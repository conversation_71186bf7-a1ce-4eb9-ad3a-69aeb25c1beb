<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Calculator Widget Test</title>
    <link rel="stylesheet" href="style.css">
    <style>
        body {
            font-family: 'Montserrat', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
        }
        
        .test-container {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 100%;
        }
        
        .test-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 1rem;
            text-align: center;
        }
        
        .test-description {
            color: #6b7280;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .test-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .test-btn {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-size: 0.875rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
        }
        
        .test-results {
            margin-top: 2rem;
            padding: 1rem;
            background: #f9fafb;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        
        .test-result {
            margin: 0.5rem 0;
            padding: 0.5rem;
            border-radius: 4px;
        }
        
        .test-result.pass {
            background: #ecfdf5;
            color: #047857;
            border: 1px solid #a7f3d0;
        }
        
        .test-result.fail {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">Calculator Widget Test</h1>
        <p class="test-description">
            This page tests the calculator widget functionality. The calculator should only be visible 
            when the assessment level is set to "GCSEPart2".
        </p>
        
        <div class="test-buttons">
            <button class="test-btn" onclick="testCalculatorVisibility()">Test Visibility</button>
            <button class="test-btn" onclick="testCalculatorFunctionality()">Test Functionality</button>
            <button class="test-btn" onclick="testCalculatorAccessibility()">Test Accessibility</button>
            <button class="test-btn" onclick="testCalculatorDragging()">Test Dragging</button>
        </div>
        
        <div id="test-results" class="test-results" style="display: none;">
            <h3>Test Results:</h3>
            <div id="results-container"></div>
        </div>
    </div>

    <!-- Calculator Widget (copied from math.html) -->
    <div id="calculator-widget" class="calculator-widget hidden">
        <!-- Calculator Toggle Button -->
        <button id="calculator-toggle-btn" class="calculator-toggle-btn" 
                aria-label="Open Calculator" 
                title="Open Calculator">
            <svg class="calculator-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="4" y="2" width="16" height="20" rx="2"/>
                <line x1="8" y1="6" x2="16" y2="6"/>
                <line x1="8" y1="10" x2="16" y2="10"/>
                <line x1="8" y1="14" x2="16" y2="14"/>
                <line x1="8" y1="18" x2="16" y2="18"/>
                <line x1="12" y1="6" x2="12" y2="18"/>
            </svg>
            <span class="calculator-label">Calculator</span>
        </button>

        <!-- Calculator Modal -->
        <div id="calculator-modal" class="calculator-modal hidden">
            <div class="calculator-container" id="calculator-container">
                <!-- Calculator Header -->
                <div class="calculator-header">
                    <span class="calculator-title">Calculator</span>
                    <button id="calculator-close-btn" class="calculator-close-btn" 
                            aria-label="Close Calculator" 
                            title="Close Calculator">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="18" y1="6" x2="6" y2="18"/>
                            <line x1="6" y1="6" x2="18" y2="18"/>
                        </svg>
                    </button>
                </div>

                <!-- Calculator Display -->
                <div class="calculator-display">
                    <div id="calculator-screen" class="calculator-screen" 
                         aria-live="polite" 
                         aria-label="Calculator display">0</div>
                </div>

                <!-- Calculator Buttons -->
                <div class="calculator-buttons">
                    <!-- Row 1: Clear and Operations -->
                    <button class="calc-btn calc-btn-clear" data-action="clear" 
                            aria-label="Clear all">C</button>
                    <button class="calc-btn calc-btn-clear" data-action="clear-entry" 
                            aria-label="Clear entry">CE</button>
                    <button class="calc-btn calc-btn-operation" data-action="backspace" 
                            aria-label="Backspace">⌫</button>
                    <button class="calc-btn calc-btn-operation" data-operation="/" 
                            aria-label="Divide">÷</button>

                    <!-- Row 2: Numbers 7-9 and Multiply -->
                    <button class="calc-btn calc-btn-number" data-number="7" 
                            aria-label="Seven">7</button>
                    <button class="calc-btn calc-btn-number" data-number="8" 
                            aria-label="Eight">8</button>
                    <button class="calc-btn calc-btn-number" data-number="9" 
                            aria-label="Nine">9</button>
                    <button class="calc-btn calc-btn-operation" data-operation="*" 
                            aria-label="Multiply">×</button>

                    <!-- Row 3: Numbers 4-6 and Subtract -->
                    <button class="calc-btn calc-btn-number" data-number="4" 
                            aria-label="Four">4</button>
                    <button class="calc-btn calc-btn-number" data-number="5" 
                            aria-label="Five">5</button>
                    <button class="calc-btn calc-btn-number" data-number="6" 
                            aria-label="Six">6</button>
                    <button class="calc-btn calc-btn-operation" data-operation="-" 
                            aria-label="Subtract">−</button>

                    <!-- Row 4: Numbers 1-3 and Add -->
                    <button class="calc-btn calc-btn-number" data-number="1" 
                            aria-label="One">1</button>
                    <button class="calc-btn calc-btn-number" data-number="2" 
                            aria-label="Two">2</button>
                    <button class="calc-btn calc-btn-number" data-number="3" 
                            aria-label="Three">3</button>
                    <button class="calc-btn calc-btn-operation" data-operation="+" 
                            aria-label="Add">+</button>

                    <!-- Row 5: Zero, Decimal, and Equals -->
                    <button class="calc-btn calc-btn-number calc-btn-zero" data-number="0" 
                            aria-label="Zero">0</button>
                    <button class="calc-btn calc-btn-number" data-action="decimal" 
                            aria-label="Decimal point">.</button>
                    <button class="calc-btn calc-btn-equals" data-action="equals" 
                            aria-label="Equals">=</button>
                </div>
            </div>
        </div>
    </div>

    <script src="mathAssessment.js"></script>
    <script>
        // Initialize a test instance of MathAssessment
        let testAssessment;
        
        document.addEventListener('DOMContentLoaded', function() {
            testAssessment = new MathAssessment();
            testAssessment.currentLevel = 'GCSEPart2'; // Set to GCSE Part 2 for testing
            testAssessment.showCalculatorWidget(); // Show calculator for testing
        });
        
        function addTestResult(message, passed) {
            const resultsContainer = document.getElementById('results-container');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${passed ? 'pass' : 'fail'}`;
            resultDiv.textContent = `${passed ? '✅' : '❌'} ${message}`;
            resultsContainer.appendChild(resultDiv);
            
            // Show results container
            document.getElementById('test-results').style.display = 'block';
        }
        
        function clearTestResults() {
            const resultsContainer = document.getElementById('results-container');
            resultsContainer.innerHTML = '';
            document.getElementById('test-results').style.display = 'none';
        }
        
        function testCalculatorVisibility() {
            clearTestResults();
            
            const widget = document.getElementById('calculator-widget');
            const toggleBtn = document.getElementById('calculator-toggle-btn');
            
            addTestResult('Calculator widget exists', widget !== null);
            addTestResult('Calculator toggle button exists', toggleBtn !== null);
            addTestResult('Calculator widget is visible', !widget.classList.contains('hidden'));
        }
        
        function testCalculatorFunctionality() {
            clearTestResults();
            
            if (!testAssessment || !testAssessment.calculator) {
                addTestResult('Calculator instance not found', false);
                return;
            }
            
            const calc = testAssessment.calculator;
            
            // Test basic arithmetic
            calc.reset();
            calc.inputNumber('5');
            calc.inputOperation('+');
            calc.inputNumber('3');
            calc.performAction('equals');
            addTestResult('5 + 3 = 8', calc.getDisplayValue() === '8');
            
            // Test multiplication
            calc.reset();
            calc.inputNumber('4');
            calc.inputOperation('*');
            calc.inputNumber('6');
            calc.performAction('equals');
            addTestResult('4 × 6 = 24', calc.getDisplayValue() === '24');
            
            // Test division
            calc.reset();
            calc.inputNumber('15');
            calc.inputOperation('/');
            calc.inputNumber('3');
            calc.performAction('equals');
            addTestResult('15 ÷ 3 = 5', calc.getDisplayValue() === '5');
            
            // Test decimal
            calc.reset();
            calc.inputNumber('2');
            calc.performAction('decimal');
            calc.inputNumber('5');
            addTestResult('Decimal input: 2.5', calc.getDisplayValue() === '2.5');
            
            // Test clear
            calc.performAction('clear');
            addTestResult('Clear function', calc.getDisplayValue() === '0');
        }
        
        function testCalculatorAccessibility() {
            clearTestResults();
            
            const screen = document.getElementById('calculator-screen');
            const buttons = document.querySelectorAll('.calc-btn');
            
            addTestResult('Calculator screen has aria-live', screen.hasAttribute('aria-live'));
            addTestResult('Calculator screen has aria-label', screen.hasAttribute('aria-label'));
            
            let allButtonsHaveLabels = true;
            buttons.forEach(btn => {
                if (!btn.hasAttribute('aria-label')) {
                    allButtonsHaveLabels = false;
                }
            });
            addTestResult('All buttons have aria-labels', allButtonsHaveLabels);
            
            // Test if live region is created
            const liveRegion = document.getElementById('calculator-live-region');
            addTestResult('Screen reader live region exists', liveRegion !== null);
        }
        
        function testCalculatorDragging() {
            clearTestResults();
            
            const container = document.getElementById('calculator-container');
            const header = document.querySelector('.calculator-header');
            
            addTestResult('Calculator container exists', container !== null);
            addTestResult('Calculator header exists for dragging', header !== null);
            addTestResult('Drag state initialized', testAssessment.dragState !== undefined);
            
            // Test if drag methods exist
            addTestResult('Start drag method exists', typeof testAssessment.startCalculatorDrag === 'function');
            addTestResult('Handle drag method exists', typeof testAssessment.handleCalculatorDrag === 'function');
            addTestResult('End drag method exists', typeof testAssessment.endCalculatorDrag === 'function');
        }
    </script>
</body>
</html>
