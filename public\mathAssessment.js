/**
 * Mathematics Assessment Module
 * Handles the mathematics assessment flow for student users
 */

class MathAssessment {
  constructor() {
    this.currentLevel = 'Entry';
    this.timeLimit = 30 * 60; // Default 30 minutes in seconds
    this.timeRemaining = this.timeLimit;
    this.timerInterval = null;
    this.isSubmitted = false;
    
    // Assessment data
    this.questions = [];
    this.currentQuestionIndex = 0;
    this.answers = [];
    this.assessmentId = null;
    
    // Assessment metadata
    this.assessmentStartTime = null;
    this.questionStartTimes = [];
    
    // Level specifications
    this.levelSpecs = {
      'Entry': { timeLimit: 30 * 60, questionCount: 22, passingScore: 24, maxScore: 44 },
      'Level1': { timeLimit: 30 * 60, questionCount: 13, passingScore: 16, maxScore: 26 },
      'GCSEPart1': { timeLimit: 15 * 60, questionCount: 7, passingScore: 5, maxScore: 10 },
      'GCSEPart2': { timeLimit: 20 * 60, questionCount: 10, passingScore: 8, maxScore: 20 }
    };
  }

  /**
   * Initialize the mathematics assessment
   */
  async init() {
    this.setupEventListeners();
    console.log('Mathematics assessment initialized');

    // Auto-initialize if page is loaded directly
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.autoInit());
    } else {
      this.autoInit();
    }
  }

  /**
   * Auto-initialize the assessment interface
   */
  autoInit() {
    // Ensure proper initial state
    this.resetToInitialState();
  }

  /**
   * Reset to initial state - only user form visible
   */
  resetToInitialState() {
    // Hide all assessment containers
    const mathContainer = document.getElementById('math-assessment-container');
    const header = document.getElementById('header');

    if (mathContainer) mathContainer.classList.add('hidden');
    if (header) header.classList.add('hidden');

    // Hide all assessment screens
    this.hideAllAssessmentScreens();

    // Show only the user form
    const userFormContainer = document.getElementById('user-form-container');
    if (userFormContainer) {
      userFormContainer.classList.remove('hidden');
    }
  }

  /**
   * Setup event listeners for the assessment
   */
  setupEventListeners() {
    // Form submission
    const userForm = document.getElementById('user-form');
    if (userForm) {
      userForm.addEventListener('submit', (e) => this.handleFormSubmit(e));
    }

    // Begin assessment button
    const beginBtn = document.getElementById('begin-assessment-btn');
    if (beginBtn) {
      beginBtn.addEventListener('click', () => this.beginAssessment());
    }

    // Next question button
    const nextBtn = document.getElementById('next-question-btn');
    if (nextBtn) {
      nextBtn.addEventListener('click', () => this.nextQuestion());
    }

    // Skip question button
    const skipBtn = document.getElementById('skip-question-btn');
    if (skipBtn) {
      skipBtn.addEventListener('click', () => this.skipQuestion());
    }

    // Answer option buttons
    const optionBtns = document.querySelectorAll('.option-btn');
    optionBtns.forEach(btn => {
      btn.addEventListener('click', (e) => this.selectOption(e));
    });

    // Numeric input
    const numericInput = document.getElementById('numeric-answer');
    if (numericInput) {
      numericInput.addEventListener('input', () => this.handleNumericInput());
    }

    // Short answer input
    const shortAnswerInput = document.getElementById('short-answer');
    if (shortAnswerInput) {
      shortAnswerInput.addEventListener('input', () => this.handleShortAnswerInput());
    }

    // Modal event listeners
    this.setupModalEventListeners();
  }

  /**
   * Setup modal event listeners
   */
  setupModalEventListeners() {
    // Close modal buttons
    const closeModalBtn = document.getElementById('close-modal-btn');
    const closeModalFooterBtn = document.getElementById('close-modal-footer-btn');

    if (closeModalBtn) {
      closeModalBtn.addEventListener('click', () => this.hideDetailedReportModal());
    }

    if (closeModalFooterBtn) {
      closeModalFooterBtn.addEventListener('click', () => this.hideDetailedReportModal());
    }



    // Close modal when clicking overlay
    const modalOverlay = document.getElementById('detailed-report-modal');
    if (modalOverlay) {
      modalOverlay.addEventListener('click', (e) => {
        if (e.target === modalOverlay) {
          this.hideDetailedReportModal();
        }
      });
    }

    // Close modal with Escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        const modal = document.getElementById('detailed-report-modal');
        if (modal && !modal.classList.contains('hidden')) {
          this.hideDetailedReportModal();
        }
      }
    });
  }

  /**
   * Handle form submission to start assessment
   */
  async handleFormSubmit(e) {
    e.preventDefault();
    
    try {
      // Get form data
      const formData = new FormData(e.target);
      const firstName = formData.get('first-name');
      const lastName = formData.get('last-name');
      const email = formData.get('email');
      const assessmentLevel = formData.get('assessment-level');
      const studentLevel = formData.get('student-level');

      // Validate required fields
      if (!firstName || !lastName || !email || !assessmentLevel || !studentLevel) {
        alert('Please fill in all required fields');
        return;
      }

      // Store user data
      this.userData = {
        firstName,
        lastName,
        email,
        assessmentLevel,
        studentLevel,
        name: `${firstName} ${lastName}`
      };

      this.currentLevel = assessmentLevel;
      
      // Update level specifications
      const specs = this.levelSpecs[this.currentLevel];
      this.timeLimit = specs.timeLimit;
      this.timeRemaining = specs.timeLimit;

      // Hide form and show instructions
      this.hideUserForm();
      this.showInstructions();

    } catch (error) {
      console.error('Error handling form submission:', error);
      alert('An error occurred. Please try again.');
    }
  }

  /**
   * Show assessment instructions
   */
  showInstructions() {
    const specs = this.levelSpecs[this.currentLevel];

    // Ensure user form is completely hidden
    this.hideUserForm();

    // Hide all assessment screens first
    this.hideAllAssessmentScreens();

    // Update instruction display
    document.getElementById('time-limit-display').textContent = `${specs.timeLimit / 60} minutes`;
    document.getElementById('question-count-display').textContent = specs.questionCount;
    document.getElementById('passing-score-display').textContent = `${specs.passingScore} points`;

    // Update header
    document.getElementById('current-level').textContent = this.currentLevel;
    document.getElementById('total-questions').textContent = specs.questionCount;

    // Show only the math assessment container and instructions
    document.getElementById('math-assessment-container').classList.remove('hidden');
    document.getElementById('assessment-instructions').classList.remove('hidden');
    document.getElementById('header').classList.remove('hidden');
  }

  /**
   * Begin the assessment
   */
  async beginAssessment() {
    try {
      // Show loading with progressive messages
      this.showProgressiveLoading();

      // Start assessment
      await this.startAssessment();

      // Hide loading and show questions
      this.hideLoading();
      this.showQuestions();

    } catch (error) {
      console.error('Error beginning assessment:', error);
      this.hideLoading();
      alert('Failed to start assessment. Please try again.');
    }
  }

  /**
   * Show progressive loading with dynamic messages
   */
  showProgressiveLoading() {
    const messages = [
      'Personalising your assessment...',
      'Preparing questions for your level...',
      'Optimizing difficulty settings...',
      'Finalizing your mathematics assessment...'
    ];

    let currentMessageIndex = 0;

    // Show initial loading
    this.showLoading(messages[currentMessageIndex]);

    // Change messages progressively
    this.loadingMessageInterval = setInterval(() => {
      currentMessageIndex++;
      if (currentMessageIndex < messages.length) {
        const loadingText = document.querySelector('.loading-text');
        if (loadingText) {
          // Fade out current message
          loadingText.style.opacity = '0.5';

          setTimeout(() => {
            loadingText.textContent = messages[currentMessageIndex];
            loadingText.style.opacity = '1';
          }, 300);
        }
      } else {
        clearInterval(this.loadingMessageInterval);
      }
    }, 1200); // Change message every 1.2 seconds
  }

  /**
   * Start the assessment by fetching questions
   */
  async startAssessment() {
    try {
      const baseUrl = window.location.protocol === 'file:'
        ? 'http://localhost:3000'
        : window.location.origin;

      // Update progress: Starting request
      this.updateLoadingProgress(25, 'Connecting to assessment server...');

      // Try to warm cache for next level in background
      this.preloadNextLevelQuestions();

      const response = await fetch(`${baseUrl}/api/math-assessments/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          level: this.currentLevel,
          email: this.userData.email,
          studentLevel: this.userData.studentLevel
        }),
      });

      // Update progress: Request sent
      this.updateLoadingProgress(50, 'Processing your assessment level...');

      if (!response.ok) {
        throw new Error(`Failed to start assessment: ${response.status}`);
      }

      const data = await response.json();

      // Update progress: Data received
      this.updateLoadingProgress(75, 'Preparing your questions...');

      this.assessmentId = data.assessmentId;
      this.questions = data.questions;
      this.currentQuestionIndex = 0;
      this.answers = [];
      this.assessmentStartTime = new Date();

      // Update progress: Almost complete
      this.updateLoadingProgress(90, 'Setting up your assessment...');

      // Start timer
      this.startTimer();

      // Final progress update
      this.updateLoadingProgress(100, 'Assessment ready!');

      console.log('Assessment started:', {
        assessmentId: this.assessmentId,
        level: this.currentLevel,
        questionCount: this.questions.length
      });

      // Small delay to show completion
      await new Promise(resolve => setTimeout(resolve, 500));

    } catch (error) {
      console.error('Error starting assessment:', error);
      throw error;
    }
  }

  /**
   * Update loading progress indicator
   */
  updateLoadingProgress(percentage, message) {
    const progressFill = document.querySelector('.progress-fill');
    const loadingText = document.querySelector('.loading-text');

    if (progressFill) {
      progressFill.style.width = `${percentage}%`;
      progressFill.style.animation = 'none'; // Stop the infinite animation
    }

    if (loadingText && message) {
      loadingText.style.opacity = '0.5';
      setTimeout(() => {
        loadingText.textContent = message;
        loadingText.style.opacity = '1';
      }, 150);
    }
  }

  /**
   * Preload questions for next level in background
   */
  async preloadNextLevelQuestions() {
    try {
      const baseUrl = window.location.protocol === 'file:'
        ? 'http://localhost:3000'
        : window.location.origin;

      // Determine next level for preloading
      const levelProgression = ['Entry', 'Level1', 'GCSEPart1', 'GCSEPart2'];
      const currentIndex = levelProgression.indexOf(this.currentLevel);
      const nextLevel = currentIndex < levelProgression.length - 1 ? levelProgression[currentIndex + 1] : null;

      if (nextLevel) {
        console.log(`🚀 Preloading questions for next level: ${nextLevel}`);

        // Start background cache warming (non-blocking)
        fetch(`${baseUrl}/api/math-assessments/cache/warm`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            levels: [nextLevel],
            background: true
          }),
        }).catch(error => {
          console.warn('Background cache warming failed:', error);
        });
      }
    } catch (error) {
      console.warn('Error in preloading:', error);
    }
  }

  /**
   * Hide all assessment screens
   */
  hideAllAssessmentScreens() {
    document.getElementById('assessment-instructions').classList.add('hidden');
    document.getElementById('assessment-questions').classList.add('hidden');
    document.getElementById('assessment-results').classList.add('hidden');
    document.getElementById('assessment-loading').classList.add('hidden');
  }

  /**
   * Show the questions interface
   */
  showQuestions() {
    // Hide all other screens first
    this.hideAllAssessmentScreens();

    // Show only questions container
    document.getElementById('assessment-questions').classList.remove('hidden');

    // Load first question
    this.loadCurrentQuestion();
  }

  /**
   * Load the current question
   */
  loadCurrentQuestion() {
    if (this.currentQuestionIndex >= this.questions.length) {
      this.completeAssessment();
      return;
    }

    const question = this.questions[this.currentQuestionIndex];
    const specs = this.levelSpecs[this.currentLevel];
    
    // Update progress
    document.getElementById('question-number').textContent = this.currentQuestionIndex + 1;
    document.getElementById('total-question-count').textContent = specs.questionCount;
    document.getElementById('current-question').textContent = this.currentQuestionIndex + 1;
    
    const progressPercent = ((this.currentQuestionIndex + 1) / specs.questionCount) * 100;
    document.getElementById('progress-fill').style.width = `${progressPercent}%`;
    
    // Update question content
    document.getElementById('question-topic').textContent = question.topic || 'Mathematics';
    document.getElementById('question-text').textContent = question.question;
    
    // Hide all answer types
    this.hideAllAnswerTypes();
    
    // Show appropriate answer type
    if (question.type === 'multiple-choice') {
      this.showMultipleChoice(question);
    } else if (question.type === 'numeric') {
      this.showNumericInput();
    } else if (question.type === 'short-answer') {
      this.showShortAnswerInput();
    } else if (question.type === 'number-line') {
      this.showNumberLineSlider(question);
    } else if (question.type === 'drag-drop') {
      this.showDragDropMatching(question);
    } else if (question.type === 'area-model') {
      this.showAreaModels(question);
    } else {
      // Handle unknown question types gracefully
      console.error(`Unknown question type: ${question.type}. Defaulting to short-answer.`);
      this.showShortAnswerInput();
    }
    
    // Reset next button
    document.getElementById('next-question-btn').disabled = true;
    
    // Record question start time
    this.questionStartTimes[this.currentQuestionIndex] = new Date();
  }

  /**
   * Hide all answer input types
   */
  hideAllAnswerTypes() {
    document.getElementById('multiple-choice-options').classList.add('hidden');
    document.getElementById('numeric-input').classList.add('hidden');
    document.getElementById('short-answer-input').classList.add('hidden');
    // Hide interactive question types
    document.getElementById('number-line-slider').classList.add('hidden');
    document.getElementById('drag-drop-matching').classList.add('hidden');
    document.getElementById('area-models').classList.add('hidden');
  }

  /**
   * Show multiple choice options
   */
  showMultipleChoice(question) {
    const container = document.getElementById('multiple-choice-options');
    const buttons = container.querySelectorAll('.option-btn');

    // Validate that question has options
    if (!question.options || !Array.isArray(question.options) || question.options.length === 0) {
      console.error('Multiple choice question missing options:', question);
      // Fall back to short answer input
      this.showShortAnswerInput();
      return;
    }

    buttons.forEach((btn, index) => {
      if (index < question.options.length) {
        btn.textContent = question.options[index];
        btn.style.display = 'block';
        btn.classList.remove('selected');
      } else {
        btn.style.display = 'none';
      }
    });
    
    container.classList.remove('hidden');
  }

  /**
   * Show numeric input
   */
  showNumericInput() {
    document.getElementById('numeric-input').classList.remove('hidden');
    document.getElementById('numeric-answer').value = '';
    document.getElementById('numeric-answer').focus();
  }

  /**
   * Show short answer input
   */
  showShortAnswerInput() {
    document.getElementById('short-answer-input').classList.remove('hidden');
    document.getElementById('short-answer').value = '';
    document.getElementById('short-answer').focus();
  }

  /**
   * Show number line slider
   */
  showNumberLineSlider(question) {
    const container = document.getElementById('number-line-slider');
    const track = document.getElementById('number-line-track');
    const handle = document.getElementById('number-line-handle');
    const valueDisplay = document.getElementById('number-line-value');
    const labelsContainer = document.getElementById('number-line-labels');

    // Set up number line parameters from question
    const { min = -10, max = 10, step = 1, snapToGrid = true } = question.numberLineConfig || {};

    // Create labels
    labelsContainer.innerHTML = '';
    const labelCount = Math.min(11, Math.abs(max - min) + 1); // Max 11 labels
    for (let i = 0; i < labelCount; i++) {
      const value = min + (i * (max - min) / (labelCount - 1));
      const label = document.createElement('span');
      label.textContent = step < 1 ? value.toFixed(1) : Math.round(value);
      labelsContainer.appendChild(label);
    }

    // Initialize handle position
    let currentValue = (min + max) / 2;
    this.updateNumberLinePosition(currentValue, min, max, handle, valueDisplay);

    // Store configuration for event handlers
    this.numberLineConfig = { min, max, step, snapToGrid };
    this.currentNumberLineValue = currentValue;

    // Set up event listeners
    this.setupNumberLineEvents(track, handle, valueDisplay, min, max, step, snapToGrid);

    container.classList.remove('hidden');
  }

  /**
   * Update number line handle position and value display
   */
  updateNumberLinePosition(value, min, max, handle, valueDisplay) {
    const percentage = ((value - min) / (max - min)) * 100;
    handle.style.left = `${Math.max(0, Math.min(100, percentage))}%`;

    const displayValue = this.numberLineConfig?.step < 1 ? value.toFixed(1) : Math.round(value);
    valueDisplay.textContent = displayValue;
    this.currentNumberLineValue = value;
  }

  /**
   * Set up number line event listeners
   */
  setupNumberLineEvents(track, handle, valueDisplay, min, max, step, snapToGrid) {
    let isDragging = false;

    // Mouse events
    const handleMouseDown = (e) => {
      isDragging = true;
      e.preventDefault();
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    };

    const handleMouseMove = (e) => {
      if (!isDragging) return;
      this.updateNumberLineFromEvent(e, track, handle, valueDisplay, min, max, step, snapToGrid);
    };

    const handleMouseUp = () => {
      isDragging = false;
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      // Enable next button when value is set
      document.getElementById('next-question-btn').disabled = false;
    };

    // Touch events for mobile
    const handleTouchStart = (e) => {
      isDragging = true;
      e.preventDefault();
    };

    const handleTouchMove = (e) => {
      if (!isDragging) return;
      e.preventDefault();
      this.updateNumberLineFromEvent(e.touches[0], track, handle, valueDisplay, min, max, step, snapToGrid);
    };

    const handleTouchEnd = () => {
      isDragging = false;
      document.getElementById('next-question-btn').disabled = false;
    };

    // Track click to jump to position
    const handleTrackClick = (e) => {
      if (e.target === handle) return;
      this.updateNumberLineFromEvent(e, track, handle, valueDisplay, min, max, step, snapToGrid);
      document.getElementById('next-question-btn').disabled = false;
    };

    // Keyboard support
    const handleKeyDown = (e) => {
      let newValue = this.currentNumberLineValue;

      switch (e.key) {
        case 'ArrowLeft':
        case 'ArrowDown':
          newValue = Math.max(min, newValue - step);
          break;
        case 'ArrowRight':
        case 'ArrowUp':
          newValue = Math.min(max, newValue + step);
          break;
        case 'Home':
          newValue = min;
          break;
        case 'End':
          newValue = max;
          break;
        default:
          return;
      }

      e.preventDefault();
      this.updateNumberLinePosition(newValue, min, max, handle, valueDisplay);
      document.getElementById('next-question-btn').disabled = false;
    };

    // Add event listeners
    handle.addEventListener('mousedown', handleMouseDown);
    handle.addEventListener('touchstart', handleTouchStart);
    handle.addEventListener('touchmove', handleTouchMove);
    handle.addEventListener('touchend', handleTouchEnd);
    handle.addEventListener('keydown', handleKeyDown);
    track.addEventListener('click', handleTrackClick);

    // Store cleanup function
    this.numberLineCleanup = () => {
      handle.removeEventListener('mousedown', handleMouseDown);
      handle.removeEventListener('touchstart', handleTouchStart);
      handle.removeEventListener('touchmove', handleTouchMove);
      handle.removeEventListener('touchend', handleTouchEnd);
      handle.removeEventListener('keydown', handleKeyDown);
      track.removeEventListener('click', handleTrackClick);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }

  /**
   * Update number line value from mouse/touch event
   */
  updateNumberLineFromEvent(event, track, handle, valueDisplay, min, max, step, snapToGrid) {
    const rect = track.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));

    let value = min + (percentage / 100) * (max - min);

    if (snapToGrid) {
      value = Math.round(value / step) * step;
      value = Math.max(min, Math.min(max, value));
    }

    this.updateNumberLinePosition(value, min, max, handle, valueDisplay);
  }

  /**
   * Set up percentage number line for testing
   */
  setupPercentageNumberLine(question) {
    const track = document.getElementById('percentage-line-track');
    const handle = document.getElementById('percentage-line-handle');
    const valueDisplay = document.getElementById('percentage-line-value');
    const labelsContainer = document.getElementById('percentage-line-labels');

    // Set up number line parameters from question
    const { min = 0, max = 1, step = 0.05, snapToGrid = true } = question.numberLineConfig || {};

    // Create labels
    labelsContainer.innerHTML = '';
    const labelCount = 11; // 0, 0.1, 0.2, ..., 1.0
    for (let i = 0; i < labelCount; i++) {
      const value = min + (i * (max - min) / (labelCount - 1));
      const label = document.createElement('span');
      label.textContent = value.toFixed(1);
      labelsContainer.appendChild(label);
    }

    // Initialize handle position
    let currentValue = (min + max) / 2;
    this.updatePercentageNumberLinePosition(currentValue, min, max, handle, valueDisplay);

    // Store configuration for event handlers
    this.numberLineConfig = { min, max, step, snapToGrid };
    this.currentNumberLineValue = currentValue;

    // Set up event listeners
    this.setupPercentageNumberLineEvents(track, handle, valueDisplay, min, max, step, snapToGrid);
  }

  /**
   * Update percentage number line handle position and value display
   */
  updatePercentageNumberLinePosition(value, min, max, handle, valueDisplay) {
    const percentage = ((value - min) / (max - min)) * 100;
    handle.style.left = `${Math.max(0, Math.min(100, percentage))}%`;

    const displayValue = value.toFixed(2);
    valueDisplay.textContent = displayValue;
    this.currentNumberLineValue = value;
  }

  /**
   * Set up percentage number line event listeners
   */
  setupPercentageNumberLineEvents(track, handle, valueDisplay, min, max, step, snapToGrid) {
    let isDragging = false;

    // Mouse events
    const handleMouseDown = (e) => {
      isDragging = true;
      e.preventDefault();
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    };

    const handleMouseMove = (e) => {
      if (!isDragging) return;
      this.updatePercentageNumberLineFromEvent(e, track, handle, valueDisplay, min, max, step, snapToGrid);
    };

    const handleMouseUp = () => {
      isDragging = false;
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    // Track click to jump to position
    const handleTrackClick = (e) => {
      if (e.target === handle) return;
      this.updatePercentageNumberLineFromEvent(e, track, handle, valueDisplay, min, max, step, snapToGrid);
    };

    // Add event listeners
    handle.addEventListener('mousedown', handleMouseDown);
    track.addEventListener('click', handleTrackClick);
  }

  /**
   * Update percentage number line value from mouse event
   */
  updatePercentageNumberLineFromEvent(event, track, handle, valueDisplay, min, max, step, snapToGrid) {
    const rect = track.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));

    let value = min + (percentage / 100) * (max - min);

    if (snapToGrid) {
      value = Math.round(value / step) * step;
      value = Math.max(min, Math.min(max, value));
    }

    this.updatePercentageNumberLinePosition(value, min, max, handle, valueDisplay);
  }

  /**
   * Show drag and drop matching
   */
  showDragDropMatching(question) {
    const container = document.getElementById('drag-drop-matching');
    const draggableItemsContainer = document.getElementById('draggable-items');
    const dropZonesContainer = document.getElementById('drop-zones');

    // Get configuration from question
    const { items = [], zones = [] } = question.dragDropConfig || {};

    // Clear containers
    draggableItemsContainer.innerHTML = '';
    dropZonesContainer.innerHTML = '';

    // Create draggable items
    items.forEach((item, index) => {
      const draggableItem = document.createElement('div');
      draggableItem.className = 'draggable-item';
      draggableItem.textContent = item.text;
      draggableItem.draggable = true;
      draggableItem.dataset.itemId = item.id || index;
      draggableItem.dataset.originalText = item.text;
      draggableItemsContainer.appendChild(draggableItem);
    });

    // Create drop zones
    zones.forEach((zone, index) => {
      const dropZone = document.createElement('div');
      dropZone.className = 'drop-zone';
      dropZone.dataset.zoneId = zone.id || index;
      dropZone.innerHTML = `
        <div class="drop-zone-label">${zone.label}</div>
        <div class="drop-zone-content">Drop here</div>
      `;
      dropZonesContainer.appendChild(dropZone);
    });

    // Initialize drag and drop state
    this.dragDropState = {
      matches: new Map(), // zoneId -> itemId
      items: items,
      zones: zones
    };

    // Set up drag and drop events
    this.setupDragDropEvents();

    // Set up reset button
    this.setupDragDropResetButton();

    container.classList.remove('hidden');
  }

  /**
   * Set up drag and drop event listeners
   */
  setupDragDropEvents() {
    const draggableItems = document.querySelectorAll('.draggable-item');
    const dropZones = document.querySelectorAll('.drop-zone');

    // Drag events for items
    draggableItems.forEach(item => {
      item.addEventListener('dragstart', (e) => {
        e.dataTransfer.setData('text/plain', e.target.dataset.itemId);
        e.target.classList.add('dragging');
      });

      item.addEventListener('dragend', (e) => {
        e.target.classList.remove('dragging');
      });

      // Touch events for mobile
      item.addEventListener('touchstart', (e) => {
        this.handleTouchStart(e);
      });

      item.addEventListener('touchmove', (e) => {
        this.handleTouchMove(e);
      });

      item.addEventListener('touchend', (e) => {
        this.handleTouchEnd(e);
      });
    });

    // Drop events for zones
    dropZones.forEach(zone => {
      zone.addEventListener('dragover', (e) => {
        e.preventDefault();
        zone.classList.add('drag-over');
      });

      zone.addEventListener('dragleave', (e) => {
        zone.classList.remove('drag-over');
      });

      zone.addEventListener('drop', (e) => {
        e.preventDefault();
        zone.classList.remove('drag-over');

        const itemId = e.dataTransfer.getData('text/plain');
        const zoneId = zone.dataset.zoneId;

        this.handleDrop(itemId, zoneId);
      });
    });

    // Store cleanup function
    this.dragDropCleanup = () => {
      draggableItems.forEach(item => {
        item.removeEventListener('dragstart', this.handleDragStart);
        item.removeEventListener('dragend', this.handleDragEnd);
      });
      dropZones.forEach(zone => {
        zone.removeEventListener('dragover', this.handleDragOver);
        zone.removeEventListener('dragleave', this.handleDragLeave);
        zone.removeEventListener('drop', this.handleDrop);
      });

      // Clean up reset button
      const resetBtn = document.getElementById('reset-drag-drop-btn');
      if (resetBtn) {
        resetBtn.removeEventListener('click', this.resetDragDropQuestion);
      }
    };
  }

  /**
   * Handle item drop into zone
   */
  handleDrop(itemId, zoneId) {
    const item = document.querySelector(`[data-item-id="${itemId}"]`);
    const zone = document.querySelector(`[data-zone-id="${zoneId}"]`);

    if (!item || !zone) return;

    // Remove item from previous zone if it was placed
    this.dragDropState.matches.forEach((value, key) => {
      if (value === itemId) {
        this.dragDropState.matches.delete(key);
        const prevZone = document.querySelector(`[data-zone-id="${key}"]`);
        if (prevZone) {
          prevZone.classList.remove('filled');
          prevZone.querySelector('.drop-zone-content').textContent = 'Drop here';
        }
      }
    });

    // Remove any existing item from this zone
    const existingItemId = this.dragDropState.matches.get(zoneId);
    if (existingItemId) {
      const existingItem = document.querySelector(`[data-item-id="${existingItemId}"]`);
      if (existingItem) {
        // Return to draggable items container
        document.getElementById('draggable-items').appendChild(existingItem);
      }
    }

    // Place new item in zone
    this.dragDropState.matches.set(zoneId, itemId);
    zone.classList.add('filled');
    zone.querySelector('.drop-zone-content').textContent = item.textContent;

    // Hide the draggable item (it's now in the zone)
    item.style.display = 'none';

    // Check if all zones are filled to enable next button
    this.checkDragDropCompletion();
  }

  /**
   * Check if drag and drop is complete
   */
  checkDragDropCompletion() {
    const totalZones = this.dragDropState.zones.length;
    const filledZones = this.dragDropState.matches.size;

    // Enable next button if at least one match is made (partial completion allowed)
    document.getElementById('next-question-btn').disabled = filledZones === 0;
  }

  /**
   * Get drag and drop answer
   */
  getDragDropAnswer() {
    const matches = {};
    this.dragDropState.matches.forEach((itemId, zoneId) => {
      const zone = this.dragDropState.zones.find(z => (z.id || this.dragDropState.zones.indexOf(z)) == zoneId);
      const item = this.dragDropState.items.find(i => (i.id || this.dragDropState.items.indexOf(i)) == itemId);

      if (zone && item) {
        matches[zone.label] = item.text;
      }
    });

    return JSON.stringify(matches);
  }

  /**
   * Touch event handlers for mobile drag and drop
   */
  handleTouchStart(e) {
    this.touchItem = e.target;
    this.touchStartPos = {
      x: e.touches[0].clientX,
      y: e.touches[0].clientY
    };

    // Add visual feedback for touch start
    this.touchItem.classList.add('dragging');

    // Prevent default to avoid scrolling
    e.preventDefault();
  }

  handleTouchMove(e) {
    if (!this.touchItem) return;

    e.preventDefault();
    const touch = e.touches[0];

    // Create visual feedback for dragging
    if (!this.touchClone) {
      this.touchClone = this.touchItem.cloneNode(true);
      this.touchClone.style.position = 'fixed';
      this.touchClone.style.pointerEvents = 'none';
      this.touchClone.style.opacity = '0.8';
      this.touchClone.style.zIndex = '1000';
      this.touchClone.style.transform = 'scale(1.05)'; // Slightly larger for visibility
      this.touchClone.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.3)'; // Enhanced shadow
      document.body.appendChild(this.touchClone);
    }

    this.touchClone.style.left = `${touch.clientX - 50}px`;
    this.touchClone.style.top = `${touch.clientY - 20}px`;

    // Highlight drop zones when dragging over them
    const elementBelow = document.elementFromPoint(touch.clientX, touch.clientY);
    const dropZone = elementBelow?.closest('.drop-zone');

    // Remove previous highlights
    document.querySelectorAll('.drop-zone').forEach(zone => {
      zone.classList.remove('drag-over');
    });

    // Add highlight to current drop zone
    if (dropZone) {
      dropZone.classList.add('drag-over');
    }
  }

  handleTouchEnd(e) {
    if (!this.touchItem) return;

    const touch = e.changedTouches[0];
    const elementBelow = document.elementFromPoint(touch.clientX, touch.clientY);
    const dropZone = elementBelow?.closest('.drop-zone');

    // Remove dragging visual feedback
    this.touchItem.classList.remove('dragging');

    // Remove all drag-over highlights
    document.querySelectorAll('.drop-zone').forEach(zone => {
      zone.classList.remove('drag-over');
    });

    if (dropZone) {
      const itemId = this.touchItem.dataset.itemId;
      const zoneId = dropZone.dataset.zoneId;
      this.handleDrop(itemId, zoneId);

      // Provide haptic feedback on successful drop (if supported)
      if (navigator.vibrate) {
        navigator.vibrate(50);
      }
    }

    // Cleanup
    if (this.touchClone) {
      document.body.removeChild(this.touchClone);
      this.touchClone = null;
    }

    this.touchItem = null;
    this.touchStartPos = null;
  }

  /**
   * Set up reset button for drag and drop
   */
  setupDragDropResetButton() {
    const resetBtn = document.getElementById('reset-drag-drop-btn');
    if (resetBtn) {
      resetBtn.addEventListener('click', () => {
        this.resetDragDropQuestion();
      });
    }
  }

  /**
   * Reset drag and drop question to initial state
   */
  resetDragDropQuestion() {
    if (!this.dragDropState) return;

    // Clear all matches
    this.dragDropState.matches.clear();

    // Reset all drop zones
    const dropZones = document.querySelectorAll('.drop-zone');
    dropZones.forEach(zone => {
      zone.classList.remove('filled');
      const contentElement = zone.querySelector('.drop-zone-content');
      if (contentElement) {
        contentElement.textContent = 'Drop here';
      }
    });

    // Return all items to draggable items container
    const draggableItemsContainer = document.getElementById('draggable-items');
    const allItems = document.querySelectorAll('.draggable-item');

    allItems.forEach(item => {
      item.style.display = 'block'; // Make sure item is visible
      draggableItemsContainer.appendChild(item); // Move back to original container
    });

    // Disable next button since no matches are made
    document.getElementById('next-question-btn').disabled = true;

    console.log('Drag and drop question reset to initial state');
  }

  /**
   * Show area models
   */
  showAreaModels(question) {
    const container = document.getElementById('area-models');
    const fractionBarsContainer = document.getElementById('fraction-bars');
    const geometricShapesContainer = document.getElementById('geometric-shapes');

    // Get configuration from question
    const { fractionBars = [], geometricShapes = [] } = question.areaModelConfig || {};

    // Clear containers
    fractionBarsContainer.innerHTML = '';
    geometricShapesContainer.innerHTML = '';

    // Initialize area model state
    this.areaModelState = {
      fractionBars: [],
      geometricShapes: [],
      shadedSegments: new Set(),
      shadedShapes: new Set()
    };

    // Create fraction bars
    fractionBars.forEach((bar, barIndex) => {
      const fractionBar = document.createElement('div');
      fractionBar.className = 'fraction-bar';
      fractionBar.dataset.barIndex = barIndex;

      // Create segments
      for (let i = 0; i < bar.segments; i++) {
        const segment = document.createElement('div');
        segment.className = 'fraction-segment';
        segment.dataset.barIndex = barIndex;
        segment.dataset.segmentIndex = i;
        segment.style.width = `${100 / bar.segments}%`;

        // Add click handler
        segment.addEventListener('click', () => {
          this.toggleFractionSegment(barIndex, i);
        });

        fractionBar.appendChild(segment);
      }

      fractionBarsContainer.appendChild(fractionBar);
      this.areaModelState.fractionBars.push({
        segments: bar.segments,
        label: bar.label || `Bar ${barIndex + 1}`
      });
    });

    // Create geometric shapes
    geometricShapes.forEach((shape, shapeIndex) => {
      const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
      svg.setAttribute('width', shape.width || 100);
      svg.setAttribute('height', shape.height || 100);
      svg.setAttribute('viewBox', `0 0 ${shape.width || 100} ${shape.height || 100}`);
      svg.classList.add('geometric-shape');
      svg.dataset.shapeIndex = shapeIndex;

      // Create shape based on type
      let shapeElement;
      switch (shape.type) {
        case 'rectangle':
          shapeElement = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
          shapeElement.setAttribute('x', '10');
          shapeElement.setAttribute('y', '10');
          shapeElement.setAttribute('width', (shape.width || 100) - 20);
          shapeElement.setAttribute('height', (shape.height || 100) - 20);
          break;
        case 'circle':
          shapeElement = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
          shapeElement.setAttribute('cx', (shape.width || 100) / 2);
          shapeElement.setAttribute('cy', (shape.height || 100) / 2);
          shapeElement.setAttribute('r', Math.min(shape.width || 100, shape.height || 100) / 2 - 10);
          break;
        case 'triangle':
          shapeElement = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
          const w = shape.width || 100;
          const h = shape.height || 100;
          shapeElement.setAttribute('points', `${w/2},10 ${w-10},${h-10} 10,${h-10}`);
          break;
        default:
          shapeElement = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
          shapeElement.setAttribute('x', '10');
          shapeElement.setAttribute('y', '10');
          shapeElement.setAttribute('width', (shape.width || 100) - 20);
          shapeElement.setAttribute('height', (shape.height || 100) - 20);
      }

      // Add click handler
      svg.addEventListener('click', () => {
        this.toggleGeometricShape(shapeIndex);
      });

      svg.appendChild(shapeElement);
      geometricShapesContainer.appendChild(svg);

      this.areaModelState.geometricShapes.push({
        type: shape.type,
        label: shape.label || `Shape ${shapeIndex + 1}`
      });
    });

    // Set up cleanup
    this.areaModelCleanup = () => {
      // Remove event listeners
      document.querySelectorAll('.fraction-segment').forEach(segment => {
        segment.removeEventListener('click', this.toggleFractionSegment);
      });
      document.querySelectorAll('.geometric-shape').forEach(shape => {
        shape.removeEventListener('click', this.toggleGeometricShape);
      });
    };

    container.classList.remove('hidden');
  }

  /**
   * Toggle fraction segment shading
   */
  toggleFractionSegment(barIndex, segmentIndex) {
    const segmentKey = `${barIndex}-${segmentIndex}`;
    const segment = document.querySelector(`[data-bar-index="${barIndex}"][data-segment-index="${segmentIndex}"]`);

    if (!segment) return;

    if (this.areaModelState.shadedSegments.has(segmentKey)) {
      this.areaModelState.shadedSegments.delete(segmentKey);
      segment.classList.remove('shaded');
    } else {
      this.areaModelState.shadedSegments.add(segmentKey);
      segment.classList.add('shaded');
    }

    this.checkAreaModelCompletion();
  }

  /**
   * Toggle geometric shape shading
   */
  toggleGeometricShape(shapeIndex) {
    const shape = document.querySelector(`[data-shape-index="${shapeIndex}"]`);

    if (!shape) return;

    if (this.areaModelState.shadedShapes.has(shapeIndex)) {
      this.areaModelState.shadedShapes.delete(shapeIndex);
      shape.classList.remove('shaded');
    } else {
      this.areaModelState.shadedShapes.add(shapeIndex);
      shape.classList.add('shaded');
    }

    this.checkAreaModelCompletion();
  }

  /**
   * Check if area model has any interaction
   */
  checkAreaModelCompletion() {
    const hasInteraction = this.areaModelState.shadedSegments.size > 0 ||
                          this.areaModelState.shadedShapes.size > 0;

    document.getElementById('next-question-btn').disabled = !hasInteraction;
  }

  /**
   * Get area model answer
   */
  getAreaModelAnswer() {
    const answer = {
      fractionBars: {},
      geometricShapes: []
    };

    // Collect fraction bar data
    this.areaModelState.fractionBars.forEach((bar, barIndex) => {
      const shadedSegments = [];
      for (let i = 0; i < bar.segments; i++) {
        if (this.areaModelState.shadedSegments.has(`${barIndex}-${i}`)) {
          shadedSegments.push(i);
        }
      }

      if (shadedSegments.length > 0) {
        answer.fractionBars[bar.label] = {
          totalSegments: bar.segments,
          shadedSegments: shadedSegments,
          fraction: `${shadedSegments.length}/${bar.segments}`
        };
      }
    });

    // Collect geometric shape data
    this.areaModelState.geometricShapes.forEach((shape, shapeIndex) => {
      if (this.areaModelState.shadedShapes.has(shapeIndex)) {
        answer.geometricShapes.push({
          type: shape.type,
          label: shape.label,
          shaded: true
        });
      }
    });

    return JSON.stringify(answer);
  }

  /**
   * Handle option selection for multiple choice
   */
  selectOption(e) {
    const selectedBtn = e.target;
    const container = document.getElementById('multiple-choice-options');
    const buttons = container.querySelectorAll('.option-btn');
    
    // Remove selection from all buttons
    buttons.forEach(btn => btn.classList.remove('selected'));
    
    // Add selection to clicked button
    selectedBtn.classList.add('selected');
    
    // Enable next button
    document.getElementById('next-question-btn').disabled = false;
  }

  /**
   * Handle numeric input
   */
  handleNumericInput() {
    const input = document.getElementById('numeric-answer');
    const value = input.value.trim();
    
    // Enable next button if there's a value
    document.getElementById('next-question-btn').disabled = value === '';
  }

  /**
   * Handle short answer input
   */
  handleShortAnswerInput() {
    const input = document.getElementById('short-answer');
    const value = input.value.trim();
    
    // Enable next button if there's a value
    document.getElementById('next-question-btn').disabled = value === '';
  }

  /**
   * Move to next question
   */
  nextQuestion() {
    this.saveCurrentAnswer();
    this.cleanupInteractiveQuestions();
    this.currentQuestionIndex++;
    this.loadCurrentQuestion();
  }

  /**
   * Skip current question
   */
  skipQuestion() {
    this.saveCurrentAnswer(''); // Save empty answer
    this.cleanupInteractiveQuestions();
    this.currentQuestionIndex++;
    this.loadCurrentQuestion();
  }

  /**
   * Clean up interactive question event listeners
   */
  cleanupInteractiveQuestions() {
    if (this.numberLineCleanup) {
      this.numberLineCleanup();
      this.numberLineCleanup = null;
    }

    if (this.dragDropCleanup) {
      this.dragDropCleanup();
      this.dragDropCleanup = null;
    }

    if (this.areaModelCleanup) {
      this.areaModelCleanup();
      this.areaModelCleanup = null;
    }

    // Reset state variables
    this.currentNumberLineValue = null;
    this.numberLineConfig = null;
    this.dragDropState = null;
    this.areaModelState = null;
  }

  /**
   * Save the current answer
   */
  saveCurrentAnswer(forcedAnswer = null) {
    const question = this.questions[this.currentQuestionIndex];
    let answer = forcedAnswer;
    
    if (answer === null) {
      if (question.type === 'multiple-choice') {
        const selectedBtn = document.querySelector('.option-btn.selected');
        answer = selectedBtn ? selectedBtn.textContent : '';
      } else if (question.type === 'numeric') {
        answer = document.getElementById('numeric-answer').value.trim();
      } else if (question.type === 'short-answer') {
        answer = document.getElementById('short-answer').value.trim();
      } else if (question.type === 'number-line') {
        answer = this.currentNumberLineValue?.toString() || '';
      } else if (question.type === 'drag-drop') {
        answer = this.getDragDropAnswer();
      } else if (question.type === 'area-model') {
        answer = this.getAreaModelAnswer();
      }
    }
    
    this.answers[this.currentQuestionIndex] = {
      questionId: question.id,
      questionType: question.type,
      topic: question.topic,
      studentAnswer: answer,
      timeSpent: new Date() - this.questionStartTimes[this.currentQuestionIndex]
    };
  }

  /**
   * Complete the assessment
   */
  async completeAssessment() {
    try {
      // Show loading
      this.showLoading('Processing your assessment...');
      
      // Stop timer
      this.stopTimer();
      
      // Submit assessment
      const results = await this.submitAssessment();
      
      // Hide loading and show results
      this.hideLoading();
      this.showResults(results);
      
    } catch (error) {
      console.error('Error completing assessment:', error);
      this.hideLoading();
      alert('Failed to complete assessment. Please try again.');
    }
  }

  /**
   * Submit assessment for grading
   */
  async submitAssessment() {
    try {
      const baseUrl = window.location.protocol === 'file:' 
        ? 'http://localhost:3000' 
        : window.location.origin;

      const timeSpent = this.timeLimit - this.timeRemaining;

      const response = await fetch(`${baseUrl}/api/math-assessments/${this.assessmentId}/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          answers: this.answers,
          email: this.userData.email,
          level: this.currentLevel,
          timeSpent: timeSpent
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to submit assessment: ${response.status}`);
      }

      const results = await response.json();
      
      console.log('Assessment submitted successfully:', results);
      return results;

    } catch (error) {
      console.error('Error submitting assessment:', error);
      throw error;
    }
  }

  /**
   * Show assessment results
   */
  showResults(results) {
    try {
      // Store results for detailed modal
      this.assessmentResults = results;

      // Hide all other screens first
      this.hideAllAssessmentScreens();

      // Show only results container
      const resultsContainer = document.getElementById('assessment-results');
      if (!resultsContainer) {
        console.error('Assessment results container not found');
        return;
      }
      resultsContainer.classList.remove('hidden');

    // Ensure results container is scrollable and focused for accessibility
    const resultsContentContainer = document.querySelector('.results-container');
    if (resultsContentContainer) {
      resultsContentContainer.setAttribute('tabindex', '0');
      setTimeout(() => {
        resultsContentContainer.focus();
        resultsContentContainer.scrollTop = 0; // Scroll to top of results
      }, 100);
    }

    // Update results display
    document.getElementById('final-score').textContent = results.score;
    document.getElementById('max-score').textContent = results.maxScore;

    // Update status with encouraging, level-based messaging
    const statusElement = document.getElementById('status-text');
    const statusBadge = statusElement.parentElement;

    // Clear previous status classes
    statusBadge.classList.remove('passed', 'not-passed', 'current-level', 'progress-made');

    if (results.passed) {
      statusElement.textContent = `${this.currentLevel} Level Complete!`;
      statusBadge.classList.add('passed');

      // Check if user can progress to next level
      this.checkLevelProgression(results);
    } else {
      // Show current achievement level instead of "Not Passed"
      const achievementLevel = this.determineAchievementLevel(results.score, results.maxScore);
      statusElement.textContent = `${achievementLevel.level} Achievement`;
      statusBadge.classList.add(achievementLevel.class);

      // Add encouraging subtitle
      this.addEncouragingMessage(achievementLevel, results);
    }

    // Show topic breakdown if available
    if (results.topicBreakdown) {
      this.displayTopicBreakdown(results.topicBreakdown);
    }

    // Show detailed feedback if available
    if (results.feedback) {
      this.displayFeedback(results.feedback);
    }

    // Show strengths and improvements
    if (results.strengths || results.improvements) {
      this.displayStrengthsAndImprovements(results.strengths, results.improvements);
    }

    // Show recommendations if available
    if (results.placementRecommendation) {
      this.displayRecommendations(results.placementRecommendation);
    }

    // Add progression options if applicable
    this.addProgressionOptions(results);

    } catch (error) {
      console.error('Error displaying assessment results:', error);
      // Show a fallback message to the user
      const fallbackContainer = document.getElementById('assessment-results');
      if (fallbackContainer) {
        fallbackContainer.innerHTML = `
          <div class="error-message">
            <h2>Assessment Complete</h2>
            <p>Your assessment has been completed successfully, but there was an issue displaying the detailed results.</p>
            <p>Your score: ${results?.score || 'N/A'} / ${results?.maxScore || 'N/A'}</p>
            <button onclick="location.reload()" class="modern-submit-btn">Refresh Page</button>
          </div>
        `;
        fallbackContainer.classList.remove('hidden');
      }
    }
  }

  /**
   * Check if user can progress to next level
   */
  checkLevelProgression(results) {
    const progressionMap = {
      'Entry': 'Level1',
      'Level1': 'GCSEPart1',
      'GCSEPart1': 'GCSEPart2',
      'GCSEPart2': null // No further progression
    };

    const nextLevel = progressionMap[this.currentLevel];

    if (nextLevel && results.passed) {
      this.nextAvailableLevel = nextLevel;
      console.log(`User passed ${this.currentLevel}, can progress to ${nextLevel}`);
    }
  }

  /**
   * Add progression options to results
   */
  addProgressionOptions(results) {
    const actionsContainer = document.querySelector('.results-actions');
    if (!actionsContainer) {
      console.warn('Results actions container not found');
      return;
    }

    // Clear existing actions
    actionsContainer.innerHTML = '';

    // Add detailed report button
    const reportBtn = document.createElement('button');
    reportBtn.id = 'view-detailed-report-btn';
    reportBtn.className = 'modern-submit-btn';
    reportBtn.innerHTML = `
      <span class="btn-text">View Detailed Report</span>
      <span class="btn-icon">📊</span>
    `;
    reportBtn.addEventListener('click', () => this.showDetailedReportModal());
    actionsContainer.appendChild(reportBtn);

    // Add progression button if user passed and next level is available
    if (results.passed && this.nextAvailableLevel) {
      const progressBtn = document.createElement('button');
      progressBtn.className = 'modern-submit-btn progression-btn';
      progressBtn.innerHTML = `
        <span class="btn-text">Continue to ${this.nextAvailableLevel}</span>
        <span class="btn-icon">→</span>
      `;
      progressBtn.addEventListener('click', () => this.progressToNextLevel());
      actionsContainer.appendChild(progressBtn);
    }

    // Add retake button if user didn't pass
    if (!results.passed) {
      const retakeBtn = document.createElement('button');
      retakeBtn.className = 'modern-submit-btn retake-btn';
      retakeBtn.innerHTML = `
        <span class="btn-text">Retake Assessment</span>
        <span class="btn-icon">🔄</span>
      `;
      retakeBtn.addEventListener('click', () => this.retakeAssessment());
      actionsContainer.appendChild(retakeBtn);
    }
  }

  /**
   * Progress to next level
   */
  async progressToNextLevel() {
    if (!this.nextAvailableLevel) {
      alert('No next level available');
      return;
    }

    try {
      // Update current level
      this.currentLevel = this.nextAvailableLevel;
      this.nextAvailableLevel = null;

      // Update level specifications
      const specs = this.levelSpecs[this.currentLevel];
      this.timeLimit = specs.timeLimit;
      this.timeRemaining = specs.timeLimit;

      // Reset assessment state
      this.questions = [];
      this.currentQuestionIndex = 0;
      this.answers = [];
      this.assessmentId = null;

      // Show instructions for new level (this will handle hiding other screens)
      this.showInstructions();

    } catch (error) {
      console.error('Error progressing to next level:', error);
      alert('Failed to progress to next level. Please try again.');
    }
  }

  /**
   * Retake current assessment
   */
  async retakeAssessment() {
    try {
      // Reset assessment state
      this.questions = [];
      this.currentQuestionIndex = 0;
      this.answers = [];
      this.assessmentId = null;
      this.timeRemaining = this.timeLimit;

      // Show instructions (this will handle hiding other screens)
      this.showInstructions();

    } catch (error) {
      console.error('Error retaking assessment:', error);
      alert('Failed to restart assessment. Please try again.');
    }
  }



  /**
   * Display topic performance breakdown
   */
  displayTopicBreakdown(topicBreakdown) {
    const container = document.getElementById('topic-breakdown');
    if (!container) {
      console.warn('Topic breakdown container not found');
      return;
    }

    container.innerHTML = '';

    // Calculate total possible points for percentage calculation
    const totalTopics = Object.keys(topicBreakdown).length;
    const maxPointsPerTopic = Math.floor(this.levelSpecs[this.currentLevel].maxScore / totalTopics);

    Object.entries(topicBreakdown).forEach(([topic, score]) => {
      const percentage = Math.round((score / maxPointsPerTopic) * 100);
      const topicElement = document.createElement('div');
      topicElement.className = 'topic-item';

      // Add performance indicator
      let performanceClass = 'low';
      if (percentage >= 80) performanceClass = 'high';
      else if (percentage >= 60) performanceClass = 'medium';

      topicElement.innerHTML = `
        <div class="topic-info">
          <span class="topic-name">${this.formatTopicName(topic)}</span>
          <span class="topic-percentage ${performanceClass}">${percentage}%</span>
        </div>
        <div class="topic-progress-bar">
          <div class="topic-progress-fill ${performanceClass}" style="width: ${percentage}%"></div>
        </div>
        <span class="topic-score">${score}/${maxPointsPerTopic}</span>
      `;
      container.appendChild(topicElement);
    });
  }

  /**
   * Display placement recommendations
   */
  displayRecommendations(recommendation) {
    const container = document.getElementById('recommendations');
    if (!container) {
      console.warn('Recommendations container not found');
      return;
    }

    container.innerHTML = `
      <div class="recommendation-header">
        <div class="recommendation-level">
          <span class="level-badge">${recommendation.level}</span>
          <span class="level-label">Recommended Level</span>
        </div>
      </div>

      <div class="recommendation-content">
        <div class="recommendation-reasoning">
          <h4>Assessment Summary</h4>
          <p>${recommendation.reasoning}</p>
        </div>

        <div class="recommendation-next-steps">
          <h4>Immediate Next Steps</h4>
          <ul class="next-steps-list">
            ${recommendation.nextSteps.map(step => `<li>${step}</li>`).join('')}
          </ul>
        </div>

        <div class="recommendation-courses">
          <h4>Recommended Courses</h4>
          <div class="course-list">
            ${recommendation.courseRecommendations.map(course => `
              <div class="course-item">
                <span class="course-icon">📚</span>
                <span class="course-name">${course}</span>
              </div>
            `).join('')}
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Display comprehensive feedback
   */
  displayFeedback(feedback) {
    const feedbackSection = document.querySelector('.feedback-section');
    if (!feedbackSection) {
      console.warn('Feedback section not found');
      return;
    }

    if (!feedback || Object.keys(feedback).length === 0) {
      feedbackSection.style.display = 'none';
      return;
    }

    const feedbackContainer = document.createElement('div');
    feedbackContainer.className = 'feedback-container';

    const feedbackAreas = [
      { key: 'numericalSkills', title: 'Numerical Skills', icon: '🔢' },
      { key: 'algebraicThinking', title: 'Algebraic Thinking', icon: '📐' },
      { key: 'problemSolving', title: 'Problem Solving', icon: '🧩' },
      { key: 'geometricReasoning', title: 'Geometric Reasoning', icon: '📏' },
      { key: 'dataHandling', title: 'Data Handling', icon: '📊' }
    ];

    feedbackAreas.forEach(area => {
      if (feedback[area.key]) {
        const feedbackItem = document.createElement('div');
        feedbackItem.className = 'feedback-item';
        feedbackItem.innerHTML = `
          <div class="feedback-header">
            <span class="feedback-icon">${area.icon}</span>
            <h4 class="feedback-title">${area.title}</h4>
          </div>
          <p class="feedback-text">${feedback[area.key]}</p>
        `;
        feedbackContainer.appendChild(feedbackItem);
      }
    });

    // Add overall feedback if available
    if (feedback.overall) {
      const overallFeedback = document.createElement('div');
      overallFeedback.className = 'feedback-item overall-feedback';
      overallFeedback.innerHTML = `
        <div class="feedback-header">
          <span class="feedback-icon">🎯</span>
          <h4 class="feedback-title">Overall Assessment</h4>
        </div>
        <p class="feedback-text">${feedback.overall}</p>
      `;
      feedbackContainer.appendChild(overallFeedback);
    }

    // Replace existing content
    feedbackSection.innerHTML = '<h3>Your Performance Analysis</h3>';
    feedbackSection.appendChild(feedbackContainer);
  }

  /**
   * Display strengths and improvements
   */
  displayStrengthsAndImprovements(strengths, improvements) {
    const container = document.querySelector('.feedback-section');
    if (!container) {
      console.warn('Feedback section not found for strengths and improvements');
      return;
    }

    if (strengths && strengths.length > 0) {
      const strengthsDiv = document.createElement('div');
      strengthsDiv.className = 'strengths-section';
      strengthsDiv.innerHTML = `
        <h4 class="section-title positive">✅ Your Strengths</h4>
        <ul class="strengths-list">
          ${strengths.map(strength => `<li class="strength-item">${strength}</li>`).join('')}
        </ul>
      `;
      container.appendChild(strengthsDiv);
    }

    if (improvements && improvements.length > 0) {
      const improvementsDiv = document.createElement('div');
      improvementsDiv.className = 'improvements-section';
      improvementsDiv.innerHTML = `
        <h4 class="section-title improvement">📈 Areas for Development</h4>
        <ul class="improvements-list">
          ${improvements.map(improvement => `<li class="improvement-item">${improvement}</li>`).join('')}
        </ul>
      `;
      container.appendChild(improvementsDiv);
    }
  }

  /**
   * Format topic name for display
   */
  formatTopicName(topic) {
    return topic.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
  }

  /**
   * Determine achievement level based on score percentage
   */
  determineAchievementLevel(score, maxScore) {
    const percentage = (score / maxScore) * 100;

    if (percentage >= 80) {
      return { level: 'Excellent Progress', class: 'excellent-progress' };
    } else if (percentage >= 60) {
      return { level: 'Good Progress', class: 'good-progress' };
    } else if (percentage >= 40) {
      return { level: 'Steady Progress', class: 'steady-progress' };
    } else if (percentage >= 20) {
      return { level: 'Foundation Building', class: 'foundation-building' };
    } else {
      return { level: 'Getting Started', class: 'getting-started' };
    }
  }

  /**
   * Add encouraging message based on achievement level
   */
  addEncouragingMessage(achievementLevel, results) {
    // Create or update encouraging message element
    let messageElement = document.getElementById('encouraging-message');
    if (!messageElement) {
      messageElement = document.createElement('div');
      messageElement.id = 'encouraging-message';
      messageElement.className = 'encouraging-message';

      // Insert after status badge
      const statusBadge = document.getElementById('pass-status');
      if (statusBadge && statusBadge.parentNode) {
        statusBadge.parentNode.insertBefore(messageElement, statusBadge.nextSibling);
      } else {
        console.warn('Status badge not found for encouraging message');
        return;
      }
    }

    // Generate encouraging message based on performance
    const messages = {
      'excellent-progress': [
        'Outstanding work! You\'re demonstrating strong mathematical skills.',
        'You\'re very close to mastering this level. Keep up the excellent progress!'
      ],
      'good-progress': [
        'Great job! You\'re making solid progress in your mathematical journey.',
        'You\'ve shown good understanding. A bit more practice will get you to the next level!'
      ],
      'steady-progress': [
        'Well done! You\'re building a solid foundation in mathematics.',
        'You\'re on the right track. Continue practicing to strengthen your skills!'
      ],
      'foundation-building': [
        'Good start! You\'re developing important mathematical foundations.',
        'Every step counts in your learning journey. Keep building those skills!'
      ],
      'getting-started': [
        'You\'ve taken the first step in your mathematical journey!',
        'Learning mathematics takes time. You\'re building important foundations!'
      ]
    };

    const levelMessages = messages[achievementLevel.class] || messages['getting-started'];
    const randomMessage = levelMessages[Math.floor(Math.random() * levelMessages.length)];

    messageElement.innerHTML = `
      <div class="message-content">
        <span class="message-icon">🌟</span>
        <p class="message-text">${randomMessage}</p>
      </div>
    `;
  }

  /**
   * Start the assessment timer
   */
  startTimer() {
    this.timerInterval = setInterval(() => {
      this.timeRemaining--;
      this.updateTimerDisplay();
      
      if (this.timeRemaining <= 0) {
        this.completeAssessment();
      }
    }, 1000);
  }

  /**
   * Stop the assessment timer
   */
  stopTimer() {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
      this.timerInterval = null;
    }
  }

  /**
   * Update timer display
   */
  updateTimerDisplay() {
    const minutes = Math.floor(this.timeRemaining / 60);
    const seconds = this.timeRemaining % 60;
    const timeString = `${minutes}:${seconds.toString().padStart(2, '0')}`;
    
    document.getElementById('timer').textContent = timeString;
    document.getElementById('timer-display').textContent = timeString;
  }

  /**
   * Show loading screen with enhanced animations
   */
  showLoading(message) {
    // Hide all other screens first
    this.hideAllAssessmentScreens();

    // Show loading screen
    document.getElementById('assessment-loading').classList.remove('hidden');
    document.querySelector('.loading-text').textContent = message;

    // Start progress animation
    this.animateProgressSteps();
  }

  /**
   * Animate progress steps during loading
   */
  animateProgressSteps() {
    const steps = document.querySelectorAll('.step');
    if (steps.length === 0) return;

    // Reset all steps
    steps.forEach(step => {
      step.classList.remove('active', 'completed');
    });

    // Animate steps sequentially
    let currentStep = 0;
    const stepInterval = setInterval(() => {
      if (currentStep > 0) {
        steps[currentStep - 1].classList.remove('active');
        steps[currentStep - 1].classList.add('completed');
      }

      if (currentStep < steps.length) {
        steps[currentStep].classList.add('active');
        currentStep++;
      } else {
        clearInterval(stepInterval);
        // Mark last step as completed after a brief delay
        setTimeout(() => {
          if (steps[steps.length - 1]) {
            steps[steps.length - 1].classList.remove('active');
            steps[steps.length - 1].classList.add('completed');
          }
        }, 500);
      }
    }, 1000); // Change step every second

    // Store interval reference for cleanup
    this.progressInterval = stepInterval;
  }

  /**
   * Hide loading screen
   */
  hideLoading() {
    document.getElementById('assessment-loading').classList.add('hidden');

    // Clean up progress animation
    if (this.progressInterval) {
      clearInterval(this.progressInterval);
      this.progressInterval = null;
    }

    // Clean up loading message interval
    if (this.loadingMessageInterval) {
      clearInterval(this.loadingMessageInterval);
      this.loadingMessageInterval = null;
    }
  }

  /**
   * Hide user form
   */
  hideUserForm() {
    const userFormContainer = document.getElementById('user-form-container');
    if (userFormContainer) {
      userFormContainer.classList.add('hidden');
    }
  }

  /**
   * Test method to verify container visibility flow
   * Can be called from browser console: window.mathAssessment.testContainerFlow()
   */
  testContainerFlow() {
    console.log('Testing container visibility flow...');

    const containers = [
      'user-form-container',
      'math-assessment-container',
      'assessment-instructions',
      'assessment-questions',
      'assessment-results',
      'assessment-loading',
      'header'
    ];

    const getVisibilityState = () => {
      const state = {};
      containers.forEach(id => {
        const element = document.getElementById(id);
        state[id] = element ? !element.classList.contains('hidden') : 'not found';
      });
      return state;
    };

    console.log('1. Initial state:', getVisibilityState());

    // Test reset to initial state
    this.resetToInitialState();
    console.log('2. After resetToInitialState():', getVisibilityState());

    // Test show instructions
    this.currentLevel = 'Entry';
    this.showInstructions();
    console.log('3. After showInstructions():', getVisibilityState());

    // Test show loading
    this.showLoading('Test loading...');
    console.log('4. After showLoading():', getVisibilityState());

    // Test hide loading and show questions
    this.hideLoading();
    this.showQuestions();
    console.log('5. After showQuestions():', getVisibilityState());

    // Reset back to initial state
    this.resetToInitialState();
    console.log('6. Final reset to initial state:', getVisibilityState());

    console.log('Container flow test completed!');
  }

  /**
   * Test responsive results display
   * Can be called from browser console: window.mathAssessment.testResponsiveResults()
   */
  testResponsiveResults() {
    console.log('Testing responsive results display...');

    // Create mock results data for testing (you can modify score to test different achievement levels)
    const mockResults = {
      score: 18, // Try different values: 35+ (passed), 25-34 (excellent), 15-24 (good), 10-14 (steady), 5-9 (foundation), 0-4 (getting started)
      maxScore: 44,
      passed: false, // Will be determined by score vs passing threshold
      topicBreakdown: {
        arithmetic: 8,
        algebra: 6,
        geometry: 4,
        statistics: 5,
        problemSolving: 3,
        measurement: 2
      },
      feedback: {
        numericalSkills: 'Strong performance in basic arithmetic operations. Shows good understanding of number relationships and calculation accuracy.',
        algebraicThinking: 'Demonstrates solid grasp of algebraic concepts. Can solve simple equations and work with variables effectively.',
        problemSolving: 'Good analytical approach to word problems. Shows ability to break down complex scenarios into manageable steps.',
        geometricReasoning: 'Basic understanding of geometric shapes and properties. Could benefit from more practice with angle calculations.',
        dataHandling: 'Competent in reading and interpreting basic charts and graphs. Shows understanding of averages and data trends.',
        overall: 'Excellent overall performance demonstrating readiness for Level 1 mathematics. Strong foundation across multiple topic areas with particular strength in arithmetic and algebra.'
      },
      strengths: [
        'Excellent arithmetic calculation skills',
        'Strong algebraic reasoning and equation solving',
        'Good problem-solving methodology',
        'Effective time management during assessment'
      ],
      improvements: [
        'Practice geometric angle calculations',
        'Develop statistical analysis skills',
        'Work on complex word problem interpretation',
        'Strengthen measurement unit conversions'
      ],
      placementRecommendation: {
        level: 'Level 1',
        reasoning: 'Strong performance across multiple mathematical areas indicates readiness for Level 1 mathematics course. Particular strengths in arithmetic and algebra provide excellent foundation for progression.',
        nextSteps: [
          'Enroll in Level 1 Mathematics course',
          'Focus on geometry and statistics modules',
          'Practice advanced problem-solving techniques'
        ],
        courseRecommendations: [
          'Functional Skills Mathematics Level 1',
          'GCSE Mathematics Foundation Tier',
          'Adult Numeracy Level 1 Course'
        ]
      }
    };

    // Show the results with mock data
    this.showResults(mockResults);

    console.log('Mock results displayed. Check responsive behavior:');
    console.log('1. Results container should have scrolling if content exceeds viewport');
    console.log('2. All sections should be accessible through scrolling');
    console.log('3. Layout should adapt to screen size');
    console.log('4. Action buttons should be visible and accessible');

    // Test viewport dimensions
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight,
      devicePixelRatio: window.devicePixelRatio
    };

    console.log('Current viewport:', viewport);

    if (viewport.width < 768) {
      console.log('📱 Mobile viewport detected - verify mobile-specific styles');
    } else if (viewport.width < 1024) {
      console.log('📱 Tablet viewport detected - verify tablet-specific styles');
    } else {
      console.log('🖥️ Desktop viewport detected - verify desktop layout');
    }

    return mockResults;
  }

  /**
   * Show detailed report modal
   */
  showDetailedReportModal() {
    const modal = document.getElementById('detailed-report-modal');
    if (!modal) {
      console.error('Modal element not found');
      return;
    }

    // Populate modal with current assessment data
    this.populateDetailedReport();

    // Show modal with animation
    modal.classList.remove('hidden');
    setTimeout(() => {
      modal.classList.add('show');
    }, 10);

    // Prevent body scroll
    document.body.style.overflow = 'hidden';
  }

  /**
   * Hide detailed report modal
   */
  hideDetailedReportModal() {
    const modal = document.getElementById('detailed-report-modal');
    if (!modal) return;

    // Hide modal with animation
    modal.classList.remove('show');
    setTimeout(() => {
      modal.classList.add('hidden');
    }, 300);

    // Restore body scroll
    document.body.style.overflow = '';
  }

  /**
   * Populate detailed report modal with assessment data
   */
  populateDetailedReport() {
    if (!this.assessmentResults) {
      console.warn('No assessment results available for detailed report');
      return;
    }

    const results = this.assessmentResults;

    // Populate summary section
    this.populateModalSummary(results);

    // Populate topic breakdown
    this.populateModalTopicBreakdown(results);

    // Populate feedback
    this.populateModalFeedback(results);

    // Populate strengths
    this.populateModalStrengths(results);

    // Populate improvements
    this.populateModalImprovements(results);

    // Populate recommendations
    this.populateModalRecommendations(results);

    // Populate course recommendations
    this.populateModalCourses(results);
  }

  /**
   * Populate modal summary section
   */
  populateModalSummary(results) {
    const elements = {
      level: document.getElementById('modal-level'),
      score: document.getElementById('modal-score'),
      percentage: document.getElementById('modal-percentage'),
      status: document.getElementById('modal-status'),
      time: document.getElementById('modal-time'),
      questions: document.getElementById('modal-questions')
    };

    if (elements.level) elements.level.textContent = this.assessmentLevel || 'Entry';
    if (elements.score) elements.score.textContent = `${results.score || 0} / ${results.maxScore || 44}`;
    if (elements.percentage) {
      const percentage = Math.round(((results.score || 0) / (results.maxScore || 44)) * 100);
      elements.percentage.textContent = `${percentage}%`;
    }
    if (elements.status) {
      // Since we're showing results, the assessment is complete
      const statusText = results.passed ? 'Passed' : 'Assessment Complete';
      const statusClass = results.passed ? 'status-passed' : 'status-complete';
      elements.status.textContent = statusText;
      elements.status.className = `summary-value ${statusClass}`;
    }
    if (elements.time) {
      // Calculate time spent from timer (timeLimit - timeRemaining)
      const timeSpent = this.timeLimit - this.timeRemaining;
      const minutes = Math.floor(timeSpent / 60);
      elements.time.textContent = `${minutes} minutes`;
    }
    if (elements.questions) {
      const specs = this.levelSpecs[this.assessmentLevel] || this.levelSpecs['Entry'];
      elements.questions.textContent = specs.questionCount;
    }
  }

  /**
   * Populate modal topic breakdown
   */
  populateModalTopicBreakdown(results) {
    const container = document.getElementById('modal-topic-breakdown');
    if (!container || !results.topicBreakdown) return;

    container.innerHTML = '';

    Object.entries(results.topicBreakdown).forEach(([topic, score]) => {
      const topicItem = document.createElement('div');
      topicItem.className = 'topic-item-detailed';

      const maxScore = this.getMaxScoreForTopic(topic);
      const percentage = maxScore > 0 ? Math.round((score / maxScore) * 100) : 0;

      topicItem.innerHTML = `
        <div class="topic-header">
          <span class="topic-name">${this.formatTopicName(topic)}</span>
          <span class="topic-score">${score} / ${maxScore}</span>
        </div>
        <div class="topic-progress">
          <div class="topic-progress-fill" style="width: ${percentage}%"></div>
        </div>
        <div class="topic-description">
          ${this.getTopicDescription(topic)}
        </div>
      `;

      container.appendChild(topicItem);
    });
  }

  /**
   * Populate modal feedback section
   */
  populateModalFeedback(results) {
    const container = document.getElementById('modal-feedback');
    if (!container || !results.feedback) return;

    container.innerHTML = '';

    Object.entries(results.feedback).forEach(([category, text]) => {
      const feedbackItem = document.createElement('div');
      feedbackItem.className = 'feedback-item';

      feedbackItem.innerHTML = `
        <div class="feedback-category">${this.formatFeedbackCategory(category)}</div>
        <div class="feedback-text">${text}</div>
      `;

      container.appendChild(feedbackItem);
    });
  }

  /**
   * Populate modal strengths section
   */
  populateModalStrengths(results) {
    const container = document.getElementById('modal-strengths');
    if (!container || !results.strengths) return;

    container.innerHTML = '';

    results.strengths.forEach(strength => {
      const strengthItem = document.createElement('div');
      strengthItem.className = 'strength-item';

      strengthItem.innerHTML = `
        <div class="item-icon">✅</div>
        <div class="item-text">${strength}</div>
      `;

      container.appendChild(strengthItem);
    });
  }

  /**
   * Populate modal improvements section
   */
  populateModalImprovements(results) {
    const container = document.getElementById('modal-improvements');
    if (!container || !results.improvements) return;

    container.innerHTML = '';

    results.improvements.forEach(improvement => {
      const improvementItem = document.createElement('div');
      improvementItem.className = 'improvement-item';

      improvementItem.innerHTML = `
        <div class="item-icon">🎯</div>
        <div class="item-text">${improvement}</div>
      `;

      container.appendChild(improvementItem);
    });
  }

  /**
   * Populate modal recommendations section
   */
  populateModalRecommendations(results) {
    const container = document.getElementById('modal-recommendations');
    if (!container || !results.placementRecommendation) return;

    container.innerHTML = '';

    const recommendation = results.placementRecommendation;

    const recommendationItem = document.createElement('div');
    recommendationItem.className = 'recommendation-item';

    recommendationItem.innerHTML = `
      <div class="recommendation-title">Recommended Level: ${recommendation.level}</div>
      <div class="recommendation-description">${recommendation.reasoning}</div>
    `;

    container.appendChild(recommendationItem);

    // Add next steps if available
    if (recommendation.nextSteps && recommendation.nextSteps.length > 0) {
      const nextStepsItem = document.createElement('div');
      nextStepsItem.className = 'recommendation-item';

      const nextStepsList = recommendation.nextSteps.map(step => `<li>${step}</li>`).join('');
      nextStepsItem.innerHTML = `
        <div class="recommendation-title">Next Steps</div>
        <div class="recommendation-description">
          <ul style="margin: 0.5rem 0; padding-left: 1.5rem;">
            ${nextStepsList}
          </ul>
        </div>
      `;

      container.appendChild(nextStepsItem);
    }
  }

  /**
   * Populate modal courses section
   */
  populateModalCourses(results) {
    const container = document.getElementById('modal-courses');
    if (!container) return;

    container.innerHTML = '';

    // Get course recommendations from placement recommendation
    const courseRecommendations = results.placementRecommendation?.courseRecommendations || [];

    if (courseRecommendations.length === 0) {
      container.innerHTML = '<div class="course-item"><div class="course-title">No specific course recommendations available</div></div>';
      return;
    }

    courseRecommendations.forEach(course => {
      const courseItem = document.createElement('div');
      courseItem.className = 'course-item';

      courseItem.innerHTML = `
        <div class="course-title">${course}</div>
        <div class="course-description">Recommended based on your assessment results</div>
      `;

      container.appendChild(courseItem);
    });
  }

  /**
   * Helper methods for formatting and data processing
   */
  formatTopicName(topic) {
    const topicNames = {
      arithmetic: 'Arithmetic',
      fractions: 'Fractions',
      percentages: 'Percentages',
      basicAlgebra: 'Basic Algebra',
      measurement: 'Measurement',
      dataHandling: 'Data Handling',
      advancedArithmetic: 'Advanced Arithmetic',
      fractionsDecimals: 'Fractions & Decimals',
      percentagesRatio: 'Percentages & Ratio',
      algebraicExpressions: 'Algebraic Expressions',
      geometry: 'Geometry',
      statistics: 'Statistics'
    };
    return topicNames[topic] || topic.charAt(0).toUpperCase() + topic.slice(1);
  }

  formatFeedbackCategory(category) {
    const categoryNames = {
      numericalSkills: 'Numerical Skills',
      algebraicThinking: 'Algebraic Thinking',
      problemSolving: 'Problem Solving',
      geometricReasoning: 'Geometric Reasoning',
      dataHandling: 'Data Handling',
      overall: 'Overall Assessment'
    };
    return categoryNames[category] || category.charAt(0).toUpperCase() + category.slice(1);
  }

  getMaxScoreForTopic(topic) {
    // This would typically come from the assessment configuration
    // For now, return a reasonable default based on typical question distribution
    const topicMaxScores = {
      arithmetic: 8,
      fractions: 6,
      percentages: 4,
      basicAlgebra: 6,
      measurement: 4,
      dataHandling: 4,
      advancedArithmetic: 6,
      fractionsDecimals: 4,
      percentagesRatio: 4,
      algebraicExpressions: 6,
      geometry: 4,
      statistics: 4
    };
    return topicMaxScores[topic] || 4;
  }

  getTopicDescription(topic) {
    const descriptions = {
      arithmetic: 'Basic mathematical operations including addition, subtraction, multiplication, and division',
      fractions: 'Understanding and working with fractions, including equivalent fractions and basic operations',
      percentages: 'Converting between percentages, decimals, and fractions, and solving percentage problems',
      basicAlgebra: 'Introduction to algebraic concepts including variables, simple equations, and expressions',
      measurement: 'Units of measurement, conversions, and practical measurement applications',
      dataHandling: 'Reading and interpreting charts, graphs, and basic statistical concepts'
    };
    return descriptions[topic] || 'Mathematical concepts and problem-solving skills';
  }



  /**
   * Test function to verify modal functionality
   */
  testModalFunctionality() {
    console.log('Testing modal functionality...');

    // Create mock results if none exist
    if (!this.assessmentResults) {
      console.log('Creating mock assessment results...');
      this.assessmentResults = {
        score: 20,
        maxScore: 44,
        passed: false,
        topicBreakdown: {
          arithmetic: 6,
          fractions: 4,
          percentages: 2,
          basicAlgebra: 3,
          measurement: 3,
          dataHandling: 2
        },
        feedback: {
          numericalSkills: "Good understanding of basic arithmetic operations.",
          algebraicThinking: "Needs improvement in algebraic concepts and problem-solving.",
          problemSolving: "Shows potential but requires more practice with word problems.",
          overall: "Solid foundation with room for improvement in advanced topics."
        },
        strengths: [
          "Strong performance in basic arithmetic calculations",
          "Good understanding of measurement concepts",
          "Consistent approach to problem-solving"
        ],
        improvements: [
          "Practice more fraction operations and conversions",
          "Work on percentage calculations and applications",
          "Strengthen algebraic thinking and equation solving"
        ],
        placementRecommendation: {
          level: "Entry Support",
          reasoning: "Student shows good foundational skills but needs additional support in key areas before progressing.",
          nextSteps: [
            "Focus on fraction operations and decimal conversions",
            "Practice percentage problems in real-world contexts",
            "Build confidence with basic algebraic concepts"
          ],
          courseRecommendations: [
            "Entry Level Functional Skills Mathematics",
            "Basic Numeracy Workshop",
            "Fractions and Decimals Masterclass"
          ]
        }
      };

      this.assessmentLevel = 'Entry';
      // Set up time tracking for test (simulate 25 minutes spent)
      this.timeLimit = 30 * 60; // 30 minutes
      this.timeRemaining = 5 * 60; // 5 minutes remaining = 25 minutes spent
    }

    console.log('Opening modal...');
    this.showDetailedReportModal();

    return 'Modal test initiated. Check if modal opened correctly.';
  }
}

// Global cache warming function
async function warmMathAssessmentCache() {
  try {
    const baseUrl = window.location.protocol === 'file:'
      ? 'http://localhost:3000'
      : window.location.origin;

    console.log('🔥 Starting background cache warming for mathematics assessment...');

    // Warm cache for all levels in background
    fetch(`${baseUrl}/api/math-assessments/cache/warm`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        levels: ['Entry', 'Level1', 'GCSEPart1', 'GCSEPart2'],
        background: true
      }),
    }).then(response => {
      if (response.ok) {
        console.log('✅ Background cache warming initiated successfully');
      }
    }).catch(error => {
      console.warn('⚠️ Background cache warming failed:', error);
    });
  } catch (error) {
    console.warn('Error in cache warming:', error);
  }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  console.log('Mathematics Assessment page loaded');

  // Initialize the assessment if not already done
  if (!window.mathAssessment) {
    window.mathAssessment = new MathAssessment();
  }

  // Start background cache warming after a short delay
  setTimeout(warmMathAssessmentCache, 1000);
});
