<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-database.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-firestore.js"></script>
    <link rel="stylesheet" type="text/css" href="style.css" />
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bodymovin/5.7.8/lottie.min.js"></script>
    <title>Mathematics Assessment - Skills Gap Analyzer</title>
</head>

<body class="bg-cover bg-center h-screen w-screen overflow-hidden bg-fixed flex items-center justify-center">
    <!-- Header with progress information -->
    <div id="header" class="fixed top-0 left-0 right-0 p-5 bg-blue-900 flex items-center justify-between h-12 text-white text-sm shadow-md hidden">
        <div class="header-texts flex justify-between w-full items-center">
            <h2 class="text-xs">Mathematics Assessment</h2>
            <h2 class="text-xs">Level: <span id="current-level">Entry</span></h2>
            <h2 class="text-xs">Question <span id="current-question">1</span> of <span id="total-questions">22</span></h2>
            <h2 class="text-xs">Time: <span id="timer-display">30:00</span></h2>
        </div>
    </div>

    <!-- User Information Form -->
    <div id="user-form-container" class="hidden">
        <div class="modern-form-container">
            <div class="form-header">
                <h2>Mathematics Assessment</h2>
                <p>Please provide your information to start your mathematics assessment</p>
            </div>

            <div class="form-content">
                <form id="user-form" class="modern-form">
                    <!-- Personal Information Section -->
                    <div class="form-section">
                        <h3 class="section-title">Personal Information</h3>

                        <div class="input-grid">
                            <div class="input-group">
                                <input
                                    type="text"
                                    id="first-name"
                                    name="first-name"
                                    required
                                    class="modern-input"
                                    placeholder="First Name"
                                >
                            </div>

                            <div class="input-group">
                                <input
                                    type="text"
                                    id="last-name"
                                    name="last-name"
                                    required
                                    class="modern-input"
                                    placeholder="Last Name"
                                >
                            </div>
                        </div>

                        <div class="input-group">
                            <input
                                type="email"
                                id="email"
                                name="email"
                                required
                                class="modern-input"
                                placeholder="Email Address"
                            >
                        </div>
                    </div>

                    <!-- Assessment Level Selection -->
                    <div class="form-section">
                        <h3 class="section-title">Assessment Level</h3>
                        <p class="section-description">Select the mathematics level you'd like to assess</p>

                        <div class="radio-group">
                            <label class="radio-option">
                                <input type="radio" name="assessment-level" value="Entry" checked>
                                <span class="radio-custom"></span>
                                <div class="radio-content">
                                    <span class="radio-title">Entry Level</span>
                                    <span class="radio-description">Basic arithmetic, fractions, percentages (30 min, 22 questions)</span>
                                </div>
                            </label>

                            <label class="radio-option">
                                <input type="radio" name="assessment-level" value="Level1">
                                <span class="radio-custom"></span>
                                <div class="radio-content">
                                    <span class="radio-title">Level 1</span>
                                    <span class="radio-description">Advanced arithmetic, algebra, geometry (30 min, 13 questions)</span>
                                </div>
                            </label>

                            <label class="radio-option">
                                <input type="radio" name="assessment-level" value="GCSEPart1">
                                <span class="radio-custom"></span>
                                <div class="radio-content">
                                    <span class="radio-title">GCSE Part 1 (Non-calculator)</span>
                                    <span class="radio-description">Number operations, algebra (15 min, 7 questions)</span>
                                </div>
                            </label>

                            <label class="radio-option">
                                <input type="radio" name="assessment-level" value="GCSEPart2">
                                <span class="radio-custom"></span>
                                <div class="radio-content">
                                    <span class="radio-title">GCSE Part 2 (Calculator)</span>
                                    <span class="radio-description">Complex calculations, statistics, trigonometry (20 min, 10 questions)</span>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- Student Level -->
                    <div class="form-section">
                        <h3 class="section-title">Student Level</h3>
                        <div class="input-group">
                            <select id="student-level" name="student-level" class="modern-select" required>
                                <option value="">Select your level</option>
                                <option value="adult-learner">Adult Learner</option>
                                <option value="returning-student">Returning Student</option>
                                <option value="school-leaver">School Leaver</option>
                                <option value="career-changer">Career Changer</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>

            <div class="form-footer">
                <div class="form-actions">
                    <button type="submit" form="user-form" id="submit-form" class="modern-submit-btn">
                        <span class="btn-text">Start Mathematics Assessment</span>
                        <span class="btn-icon">→</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Mathematics Assessment Container -->
    <div id="math-assessment-container" class="hidden">
        <!-- Assessment Instructions -->
        <div id="assessment-instructions" class="assessment-screen">
            <div class="instruction-container">
                <h2 class="instruction-title">Mathematics Assessment Instructions</h2>
                <div class="instruction-content">
                    <div class="instruction-item">
                        <span class="instruction-icon">📝</span>
                        <p>You will have <span id="time-limit-display">30 minutes</span> to complete <span id="question-count-display">22 questions</span></p>
                    </div>
                    <div class="instruction-item">
                        <span class="instruction-icon">🎯</span>
                        <p>You need <span id="passing-score-display">24 points</span> to pass this level</p>
                    </div>
                    <div class="instruction-item">
                        <span class="instruction-icon">⏰</span>
                        <p>The timer will start when you click "Begin Assessment"</p>
                    </div>
                    <div class="instruction-item">
                        <span class="instruction-icon">💡</span>
                        <p>Answer all questions to the best of your ability</p>
                    </div>
                </div>
                <button id="begin-assessment-btn" class="modern-submit-btn">
                    <span class="btn-text">Begin Assessment</span>
                    <span class="btn-icon">▶</span>
                </button>
            </div>
        </div>

        <!-- Assessment Questions -->
        <div id="assessment-questions" class="assessment-screen hidden">
            <div class="question-container">
                <div class="question-header">
                    <div class="question-progress">
                        <div class="progress-bar">
                            <div id="progress-fill" class="progress-fill"></div>
                        </div>
                        <span class="progress-text">Question <span id="question-number">1</span> of <span id="total-question-count">22</span></span>
                    </div>
                    <div class="timer-container">
                        <span id="timer">30:00</span>
                    </div>
                </div>

                <div class="question-content">
                    <div class="question-topic">
                        <span id="question-topic">Arithmetic</span>
                    </div>
                    <h3 id="question-text" class="question-text">Loading question...</h3>
                    
                    <!-- Multiple Choice Options -->
                    <div id="multiple-choice-options" class="answer-options hidden">
                        <button class="option-btn" data-option="0"></button>
                        <button class="option-btn" data-option="1"></button>
                        <button class="option-btn" data-option="2"></button>
                        <button class="option-btn" data-option="3"></button>
                    </div>

                    <!-- Numeric Input -->
                    <div id="numeric-input" class="answer-input hidden">
                        <input type="text" id="numeric-answer" class="numeric-input" placeholder="Enter your answer">
                        <p class="input-hint">Enter a number (e.g., 42 or 3.14)</p>
                    </div>

                    <!-- Short Answer Input -->
                    <div id="short-answer-input" class="answer-input hidden">
                        <input type="text" id="short-answer" class="text-input" placeholder="Enter your answer">
                        <p class="input-hint">Enter your mathematical expression</p>
                    </div>

                    <!-- Interactive Question Types -->

                    <!-- Number Line Slider -->
                    <div id="number-line-slider" class="interactive-question hidden">
                        <div class="number-line-container">
                            <div class="number-line-labels" id="number-line-labels"></div>
                            <div class="number-line-track" id="number-line-track">
                                <div class="number-line-handle" id="number-line-handle" tabindex="0" role="slider" aria-label="Number line position"></div>
                            </div>
                            <div class="number-line-value" id="number-line-value">0</div>
                        </div>
                        <p class="input-hint">Drag the handle to select your answer</p>
                    </div>

                    <!-- Drag and Drop Matching -->
                    <div id="drag-drop-matching" class="interactive-question hidden">
                        <div class="matching-container">
                            <div class="draggable-items" id="draggable-items"></div>
                            <div class="drop-zones" id="drop-zones"></div>
                        </div>
                        <div class="interactive-controls">
                            <button id="reset-drag-drop-btn" class="reset-btn" type="button">
                                <span class="btn-icon">↺</span>
                                <span class="btn-text">Reset</span>
                            </button>
                        </div>
                        <p class="input-hint">Drag items to their correct positions</p>
                    </div>

                    <!-- Interactive Area Models -->
                    <div id="area-models" class="interactive-question hidden">
                        <div class="area-model-container">
                            <div class="fraction-bars" id="fraction-bars"></div>
                            <div class="geometric-shapes" id="geometric-shapes"></div>
                        </div>
                        <p class="input-hint">Click or drag to shade the correct areas</p>
                    </div>
                </div>

                <div class="question-actions">
                    <button id="skip-question-btn" class="skip-btn">Skip Question</button>
                    <button id="next-question-btn" class="next-btn" disabled>Next Question</button>
                </div>
            </div>
        </div>

        <!-- Calculator Widget (only visible during GCSE Part 2) -->
        <div id="calculator-widget" class="calculator-widget hidden">
            <!-- Calculator Toggle Button -->
            <button id="calculator-toggle-btn" class="calculator-toggle-btn"
                    aria-label="Open Calculator"
                    title="Open Calculator">
                <svg class="calculator-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="4" y="2" width="16" height="20" rx="2"/>
                    <line x1="8" y1="6" x2="16" y2="6"/>
                    <line x1="8" y1="10" x2="16" y2="10"/>
                    <line x1="8" y1="14" x2="16" y2="14"/>
                    <line x1="8" y1="18" x2="16" y2="18"/>
                    <line x1="12" y1="6" x2="12" y2="18"/>
                </svg>
                <span class="calculator-label">Calculator</span>
            </button>

            <!-- Calculator Modal -->
            <div id="calculator-modal" class="calculator-modal hidden">
                <div class="calculator-container" id="calculator-container">
                    <!-- Calculator Header -->
                    <div class="calculator-header">
                        <span class="calculator-title">Calculator</span>
                        <button id="calculator-close-btn" class="calculator-close-btn"
                                aria-label="Close Calculator"
                                title="Close Calculator">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="18" y1="6" x2="6" y2="18"/>
                                <line x1="6" y1="6" x2="18" y2="18"/>
                            </svg>
                        </button>
                    </div>

                    <!-- Calculator Display -->
                    <div class="calculator-display">
                        <div id="calculator-screen" class="calculator-screen"
                             aria-live="polite"
                             aria-label="Calculator display">0</div>
                    </div>

                    <!-- Calculator Buttons -->
                    <div class="calculator-buttons">
                        <!-- Row 1: Clear and Operations -->
                        <button class="calc-btn calc-btn-clear" data-action="clear"
                                aria-label="Clear all">C</button>
                        <button class="calc-btn calc-btn-clear" data-action="clear-entry"
                                aria-label="Clear entry">CE</button>
                        <button class="calc-btn calc-btn-operation" data-action="backspace"
                                aria-label="Backspace">⌫</button>
                        <button class="calc-btn calc-btn-operation" data-operation="/"
                                aria-label="Divide">÷</button>

                        <!-- Row 2: Numbers 7-9 and Multiply -->
                        <button class="calc-btn calc-btn-number" data-number="7"
                                aria-label="Seven">7</button>
                        <button class="calc-btn calc-btn-number" data-number="8"
                                aria-label="Eight">8</button>
                        <button class="calc-btn calc-btn-number" data-number="9"
                                aria-label="Nine">9</button>
                        <button class="calc-btn calc-btn-operation" data-operation="*"
                                aria-label="Multiply">×</button>

                        <!-- Row 3: Numbers 4-6 and Subtract -->
                        <button class="calc-btn calc-btn-number" data-number="4"
                                aria-label="Four">4</button>
                        <button class="calc-btn calc-btn-number" data-number="5"
                                aria-label="Five">5</button>
                        <button class="calc-btn calc-btn-number" data-number="6"
                                aria-label="Six">6</button>
                        <button class="calc-btn calc-btn-operation" data-operation="-"
                                aria-label="Subtract">−</button>

                        <!-- Row 4: Numbers 1-3 and Add -->
                        <button class="calc-btn calc-btn-number" data-number="1"
                                aria-label="One">1</button>
                        <button class="calc-btn calc-btn-number" data-number="2"
                                aria-label="Two">2</button>
                        <button class="calc-btn calc-btn-number" data-number="3"
                                aria-label="Three">3</button>
                        <button class="calc-btn calc-btn-operation" data-operation="+"
                                aria-label="Add">+</button>

                        <!-- Row 5: Zero, Decimal, and Equals -->
                        <button class="calc-btn calc-btn-number calc-btn-zero" data-number="0"
                                aria-label="Zero">0</button>
                        <button class="calc-btn calc-btn-number" data-action="decimal"
                                aria-label="Decimal point">.</button>
                        <button class="calc-btn calc-btn-equals" data-action="equals"
                                aria-label="Equals">=</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Assessment Results -->
        <div id="assessment-results" class="assessment-screen hidden">
            <div class="results-container">
                <div class="results-header">
                    <h2 class="results-title">Assessment Complete!</h2>
                    <div class="results-score">
                        <span class="score-label">Your Score:</span>
                        <span id="final-score" class="score-value">0</span>
                        <span class="score-total">/ <span id="max-score">44</span></span>
                    </div>
                    <div class="result-status">
                        <div id="pass-status" class="status-badge">
                            <span id="status-text">Assessment Complete</span>
                        </div>
                    </div>
                </div>

                <div class="results-content">
                    <div class="feedback-section">
                        <h3>Your Performance</h3>
                        <div id="topic-breakdown" class="topic-breakdown">
                            <!-- Topic performance will be populated here -->
                        </div>
                    </div>

                    <div class="recommendations-section">
                        <h3>Next Steps</h3>
                        <div id="recommendations" class="recommendations">
                            <!-- Recommendations will be populated here -->
                        </div>
                    </div>
                </div>

                <div class="results-actions">
                    <button id="view-detailed-report-btn" class="modern-submit-btn">
                        <span class="btn-text">View Detailed Report</span>
                        <span class="btn-icon">📊</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Loading Screen -->
        <div id="assessment-loading" class="assessment-screen hidden">
            <div class="loading-container">
                <div class="loading-animation">
                    <div class="enhanced-spinner">
                        <div class="spinner-ring"></div>
                        <div class="spinner-ring"></div>
                        <div class="spinner-ring"></div>
                    </div>
                </div>
                <h3 class="loading-title">Processing Your Assessment</h3>
                <p class="loading-text">Personalising your assessment...</p>

                <!-- Progress indicator -->
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill"></div>
                    </div>
                    <div class="progress-steps">
                        <span class="step active">Preparing questions</span>
                        <span class="step">Customizing difficulty</span>
                        <span class="step">Finalizing assessment</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Report Modal -->
    <div id="detailed-report-modal" class="modal-overlay hidden">
        <div class="modal-container">
            <div class="modal-header">
                <h2 class="modal-title">📊 Detailed Mathematics Assessment Report</h2>
                <button id="close-modal-btn" class="modal-close-btn" aria-label="Close modal">
                    <span class="close-icon">×</span>
                </button>
            </div>

            <div class="modal-content">
                <!-- Assessment Summary -->
                <div class="report-section">
                    <h3 class="section-title">📈 Assessment Summary</h3>
                    <div class="summary-grid">
                        <div class="summary-item">
                            <span class="summary-label">Level:</span>
                            <span id="modal-level" class="summary-value">Entry</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Score:</span>
                            <span id="modal-score" class="summary-value">20 / 44</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Percentage:</span>
                            <span id="modal-percentage" class="summary-value">45%</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Status:</span>
                            <span id="modal-status" class="summary-value">In Progress</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Time Taken:</span>
                            <span id="modal-time" class="summary-value">25 minutes</span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Questions:</span>
                            <span id="modal-questions" class="summary-value">22</span>
                        </div>
                    </div>
                </div>

                <!-- Topic Breakdown -->
                <div class="report-section">
                    <h3 class="section-title">📚 Topic Performance Breakdown</h3>
                    <div id="modal-topic-breakdown" class="topic-breakdown-detailed">
                        <!-- Topic breakdown will be populated here -->
                    </div>
                </div>

                <!-- Feedback Section -->
                <div class="report-section">
                    <h3 class="section-title">💬 Detailed Feedback</h3>
                    <div id="modal-feedback" class="feedback-detailed">
                        <!-- Detailed feedback will be populated here -->
                    </div>
                </div>

                <!-- Strengths -->
                <div class="report-section">
                    <h3 class="section-title">💪 Your Strengths</h3>
                    <div id="modal-strengths" class="strengths-list">
                        <!-- Strengths will be populated here -->
                    </div>
                </div>

                <!-- Areas for Improvement -->
                <div class="report-section">
                    <h3 class="section-title">🎯 Areas for Improvement</h3>
                    <div id="modal-improvements" class="improvements-list">
                        <!-- Improvements will be populated here -->
                    </div>
                </div>

                <!-- Recommendations -->
                <div class="report-section">
                    <h3 class="section-title">🚀 Recommended Next Steps</h3>
                    <div id="modal-recommendations" class="recommendations-detailed">
                        <!-- Recommendations will be populated here -->
                    </div>
                </div>

                <!-- Course Recommendations -->
                <div class="report-section">
                    <h3 class="section-title">📖 Suggested Courses</h3>
                    <div id="modal-courses" class="courses-list">
                        <!-- Course recommendations will be populated here -->
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button id="close-modal-footer-btn" class="modal-action-btn primary">
                    <span class="btn-text">Close</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="validationFunctions.js"></script>
    <script src="interactiveQuestionExamples.js"></script>
    <script src="mathAssessment.js"></script>
    <script>
        // Initialize the mathematics assessment when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Ensure all containers are hidden initially
            document.getElementById('math-assessment-container').classList.add('hidden');
            document.getElementById('header').classList.add('hidden');

            // Show only the user form initially
            document.getElementById('user-form-container').classList.remove('hidden');

            // Initialize the mathematics assessment system
            if (typeof MathAssessment !== 'undefined') {
                window.mathAssessment = new MathAssessment();
                window.mathAssessment.init();
            } else {
                console.error('MathAssessment class not found');
            }
        });
    </script>
</body>
</html>
